<template>
  <div class="modal fade lumi-fade" tabindex="-1" id="modalRegistrarHistorico">
    <div class="modal-dialog modal-dialog-centered modal-lg">
      <div class="modal-content">
        <div class="modal-header border-0 pb-0">
          <div class="modal-title-container">
            <h4 class="modal-title mb-1">{{ modalTitle }}</h4>
            <p class="modal-subtitle text-muted mb-0">{{ modalSubtitle }}</p>
          </div>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
            ref="closeModalRegistrarHistorico"
          ></button>
        </div>

        <div class="modal-body px-4 pt-2">
          <!-- Seletor de tipo de registro -->
          <div class="tipo-registro-selector mb-4">
            <div class="row g-3">
              <div class="col-md-6">
                <div
                  class="tipo-card"
                  :class="{ 'active': tipoRegistro === 'historico' }"
                  @click="selecionarTipo('historico')"
                >
                  <div class="tipo-icon">
                    <font-awesome-icon :icon="['fas', 'clipboard-list']" />
                  </div>
                  <div class="tipo-content">
                    <h6 class="tipo-title">Anotação</h6>
                    <p class="tipo-description">Registrar um evento ou observação geral</p>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div
                  class="tipo-card"
                  :class="{ 'active': tipoRegistro === 'consulta' }"
                  @click="selecionarTipo('consulta')"
                >
                  <div class="tipo-icon">
                    <font-awesome-icon :icon="['fas', 'user-md']" />
                  </div>
                  <div class="tipo-content">
                    <h6 class="tipo-title">Consulta Realizada</h6>
                    <p class="tipo-description">Registrar uma consulta que o paciente compareceu</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Formulário -->
          <form @submit.prevent="salvarRegistro">
            <!-- Data e Horário -->
            <div class="row g-3 mb-4">
              <div class="col-md-6">
                <label for="dataRegistro" class="form-label">
                  <font-awesome-icon :icon="['fas', 'calendar']" class="me-2" />
                  Data
                </label>
                <input
                  type="date"
                  class="form-control form-control-modern"
                  id="dataRegistro"
                  v-model="registro.data"
                  required
                />
              </div>
              <div class="col-md-6">
                <label for="horarioRegistro" class="form-label">
                  <font-awesome-icon :icon="['fas', 'clock']" class="me-2" />
                  Horário
                </label>
                <input
                  type="time"
                  class="form-control form-control-modern"
                  id="horarioRegistro"
                  v-model="registro.horario"
                  required
                />
              </div>
            </div>

            <!-- Campos específicos para consulta -->
            <div v-if="tipoRegistro === 'consulta'" class="consulta-fields mb-4">
              <div class="row g-3">
                <div class="col-md-6">
                  <label for="categoriaConsulta" class="form-label">
                    <font-awesome-icon :icon="['fas', 'tags']" class="me-2" />
                    Categoria da Consulta
                  </label>
                  <select
                    class="form-select form-control-modern"
                    id="categoriaConsulta"
                    v-model="registro.categoria"
                    required
                  >
                    <option value="">Selecione a categoria</option>
                    <option
                      v-for="categoria in categorias"
                      :key="categoria.valor"
                      :value="categoria.valor"
                    >
                      {{ categoria.nome }}
                    </option>
                  </select>
                </div>
                <div class="col-md-6">
                  <label for="valorConsulta" class="form-label">
                    <font-awesome-icon :icon="['fas', 'dollar-sign']" class="me-2" />
                    Valor (opcional)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    class="form-control form-control-modern"
                    id="valorConsulta"
                    v-model="registro.valor"
                    placeholder="0,00"
                  />
                </div>
              </div>
            </div>

            <!-- Campos para anotação (histórico) -->
            <div v-if="tipoRegistro === 'historico'" class="mb-4">
              <label for="descricaoRegistro" class="form-label">
                <font-awesome-icon :icon="['fas', 'edit']" class="me-2" />
                Descrição do Histórico
              </label>
              <textarea
                class="form-control form-control-modern"
                id="descricaoRegistro"
                v-model="registro.descricao"
                rows="4"
                placeholder="Descreva o que aconteceu nesta data, observações importantes, eventos relevantes..."
                required
              ></textarea>
            </div>

            <!-- Campos para consulta realizada -->
            <div v-if="tipoRegistro === 'consulta'" class="consulta-historicos mb-4">
              <div class="row g-3">
                <div class="col-md-6">
                  <label for="historicoConsulta" class="form-label">
                    <font-awesome-icon :icon="['fas', 'clipboard-list']" class="me-2" />
                    Histórico da Consulta
                  </label>
                  <textarea
                    class="form-control form-control-modern"
                    id="historicoConsulta"
                    v-model="registro.historico_consulta"
                    rows="4"
                    placeholder="Descreva o que foi realizado durante esta consulta, procedimentos executados..."
                    required
                  ></textarea>
                </div>
                <div class="col-md-6">
                  <label for="proximaConsulta" class="form-label">
                    <font-awesome-icon :icon="['fas', 'arrow-right']" class="me-2" />
                    O que fazer na próxima consulta
                  </label>
                  <textarea
                    class="form-control form-control-modern"
                    id="proximaConsulta"
                    v-model="registro.proxima_consulta"
                    rows="4"
                    placeholder="Descreva o que deve ser feito na próxima consulta..."
                  ></textarea>
                </div>
              </div>
            </div>
          </form>
        </div>

        <div class="modal-footer border-0 pt-0">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
            Cancelar
          </button>
          <button
            type="button"
            class="btn btn-primary btn-save-modern"
            @click="salvarRegistro"
            :disabled="isLoading || !isFormValido"
          >
            <span v-if="isLoading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            <font-awesome-icon v-else :icon="['fas', 'save']" class="me-2" />
            {{ getSaveButtonText() }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment';
import { criarHistoricoPaciente } from "@/services/historicoPacienteService";
import { novaConsulta } from "@/services/consultasService";
import { getDentistas } from "@/services/dentistasService";
import cSwal from "@/utils/cSwal.js";
import { openModal, closeModal } from "@/utils/modalHelper.js";
import { mapState } from 'vuex';

export default {
  name: "RegistrarHistoricoModal",
  emits: ['historico-salvo', 'consulta-salva'],
  data() {
    return {
      isLoading: false,
      pacienteId: null,
      tipoRegistro: 'historico', // 'historico' ou 'consulta'
      dentistas: [],
      registro: {
        data: moment().format('YYYY-MM-DD'),
        horario: moment().format('HH:mm'),
        descricao: '',
        // Campos específicos para consulta
        categoria: 'acompanhamento',
        valor: '',
        historico_consulta: '',
        proxima_consulta: ''
      },
      categorias: [
        { valor: 'acompanhamento', nome: 'Acompanhamento/ativação', cor: 'info', icone: 'wrench' },
        { valor: 'primeira_consulta', nome: 'Primeira consulta', cor: 'success', icone: 'clipboard-check' },
        { valor: 'emergencia', nome: 'Emergência', cor: 'danger', icone: 'exclamation-triangle' },
        { valor: 'montagem', nome: 'Montagem', cor: 'secondary', icone: 'tools' },
        { valor: 'remocao', nome: 'Remoção', cor: 'secondary', icone: 'minus-circle' },
        { valor: 'replanejamento', nome: 'Replanejamento', cor: 'dark', icone: 'sync-alt' },
        { valor: 'pos_tratamento', nome: 'Pós-tratamento', cor: 'secondary', icone: 'check-double' }
      ]
    };
  },
  computed: {
    ...mapState(['token']),
    modalTitle() {
      return this.tipoRegistro === 'consulta'
        ? 'Registrar Consulta Realizada'
        : 'Registrar Histórico';
    },
    modalSubtitle() {
      return this.tipoRegistro === 'consulta'
        ? 'Registre uma consulta que o paciente compareceu'
        : 'Registre um evento ou observação geral';
    },
    isFormValido() {
      const baseValid = this.registro.data && this.registro.horario;

      if (this.tipoRegistro === 'consulta') {
        return baseValid &&
               this.registro.categoria &&
               this.registro.historico_consulta.trim().length > 0;
      }

      return baseValid && this.registro.descricao.trim().length > 0;
    }
  },
  methods: {
    async abrirModal(pacienteId) {
      this.pacienteId = pacienteId;
      await this.carregarDentistas();
      this.resetForm();
      openModal('modalRegistrarHistorico');
    },
    async carregarDentistas() {
      try {
        const dentistas = await getDentistas();
        if (dentistas && Array.isArray(dentistas)) {
          this.dentistas = dentistas.filter(d => d.status === 'ATIVO');
        }
      } catch (error) {
        console.error('Erro ao carregar dentistas:', error);
      }
    },
    selecionarTipo(tipo) {
      this.tipoRegistro = tipo;
      // Resetar campos específicos quando mudar o tipo
      if (tipo === 'historico') {
        this.registro.categoria = 'acompanhamento';
        this.registro.valor = '';
        this.registro.historico_consulta = '';
        this.registro.proxima_consulta = '';
      }
    },
    resetForm() {
      this.tipoRegistro = 'historico';
      this.registro = {
        data: moment().format('YYYY-MM-DD'),
        horario: moment().format('HH:mm'),
        descricao: '',
        categoria: 'acompanhamento',
        valor: '',
        historico_consulta: '',
        proxima_consulta: ''
      };
    },

    getSaveButtonText() {
      return this.tipoRegistro === 'consulta' ? 'Salvar Consulta' : 'Salvar Histórico';
    },
    async salvarRegistro() {
      if (!this.isFormValido) {
        cSwal.cAlert("Por favor, preencha todos os campos obrigatórios.");
        return;
      }

      this.isLoading = true;

      try {
        if (this.tipoRegistro === 'consulta') {
          await this.salvarConsulta();
        } else {
          await this.salvarHistorico();
        }
      } catch (error) {
        console.error("Erro ao salvar registro:", error);
        cSwal.cError("Erro ao salvar o registro. Por favor, tente novamente.");
      } finally {
        this.isLoading = false;
      }
    },
    async salvarConsulta() {
      // Usar o dentista do token do usuário logado
      const dentistaId = this.token?.dentista?.id;

      if (!dentistaId) {
        throw new Error('Dentista não encontrado no token do usuário');
      }

      const consultaData = {
        paciente_id: this.pacienteId,
        dentista_id: dentistaId,
        data: this.registro.data,
        horario: this.registro.horario,
        categoria: this.registro.categoria,
        valor: this.registro.valor ? parseFloat(this.registro.valor) : null,
        observacoes: '', // Observações vazias, pois usaremos o histórico
        status: 'realizada',
        confirmada: true
      };

      const consultaResponse = await novaConsulta(consultaData);

      if (consultaResponse) {
        // Criar o histórico da consulta com os dois campos
        const modificacoes = [
          {
            titulo: "Histórico da consulta",
            descricao: this.registro.historico_consulta
          },
          {
            titulo: "O que fazer na próxima consulta",
            descricao: this.registro.proxima_consulta || ''
          }
        ];

        const historicoData = {
          paciente_id: this.pacienteId,
          consulta_id: consultaResponse.id,
          data: this.registro.data,
          horario: this.registro.horario + ':00',
          codigo_acao: 'alteracao_consulta',
          descricao: 'Histórico de alterações da consulta',
          modificacoes: JSON.stringify(modificacoes)
        };

        await criarHistoricoPaciente(historicoData);

        cSwal.cSuccess("Consulta registrada com sucesso!");
        this.$emit('consulta-salva', consultaResponse);
        this.$emit('historico-salvo', consultaResponse); // Para compatibilidade
        closeModal('modalRegistrarHistorico');
        this.resetForm();
      } else {
        throw new Error('Falha ao criar consulta');
      }
    },
    async salvarHistorico() {
      const historicoData = {
        paciente_id: this.pacienteId,
        data: this.registro.data,
        horario: this.registro.horario + ':00', // Adicionar segundos
        codigo_acao: 'registro_manual',
        descricao: this.registro.descricao,
        referente_tratamento: false
      };

      const response = await criarHistoricoPaciente(historicoData);

      if (response) {
        cSwal.cSuccess("Histórico registrado com sucesso!");
        this.$emit('historico-salvo', response);
        closeModal('modalRegistrarHistorico');
        this.resetForm();
      } else {
        throw new Error('Falha ao criar histórico');
      }
    }
  }
};
</script>

<style scoped>
/* Modal Header */
.modal-title-container {
  flex: 1;
}

.modal-title {
  color: #2c3e50;
  font-weight: 600;
  margin: 0;
}

.modal-subtitle {
  font-size: 0.9rem;
  color: #6c757d;
}

/* Seletor de Tipo */
.tipo-registro-selector {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.tipo-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  align-items: center;
  gap: 15px;
}

.tipo-card:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
  transform: translateY(-2px);
}

.tipo-card.active {
  border-color: #007bff;
  background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.2);
}

.tipo-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.tipo-card.active .tipo-icon {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.tipo-content {
  flex: 1;
}

.tipo-title {
  margin: 0 0 5px 0;
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.tipo-description {
  margin: 0;
  color: #6c757d;
  font-size: 0.85rem;
  line-height: 1.4;
}

/* Form Controls Modernos */
.form-control-modern,
.form-select {
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px 14px;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  background: white;
  line-height: 1.4;
}

.form-control-modern:focus,
.form-select:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.08);
  outline: none;
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.form-label .fa-icon {
  color: #007bff;
  width: 16px;
}

/* Campos específicos da consulta */
.consulta-fields {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
}

.consulta-historicos {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
}

/* Botão de salvar moderno */
.btn-save-modern {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-save-modern:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
  background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
}

.btn-save-modern:disabled {
  background: #6c757d;
  transform: none;
  box-shadow: none;
}

/* Textarea */
textarea.form-control-modern {
  resize: vertical;
  min-height: 100px;
}

/* Responsividade */
@media (max-width: 768px) {
  .modal-dialog {
    margin: 10px;
  }

  .tipo-card {
    padding: 15px;
    gap: 12px;
  }

  .tipo-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .tipo-title {
    font-size: 0.9rem;
  }

  .tipo-description {
    font-size: 0.8rem;
  }

  .consulta-fields {
    padding: 15px;
  }

  .form-control-modern,
  .form-select {
    padding: 8px 12px;
    font-size: 0.85rem;
  }

  .consulta-fields,
  .consulta-historicos {
    padding: 12px;
  }
}

/* Animações */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal.show .modal-dialog {
  animation: slideInUp 0.3s ease-out;
}

/* Estados de validação - removidos para ser mais sutil */

/* Loading state */
.btn-save-modern .spinner-border-sm {
  width: 1rem;
  height: 1rem;
}
</style>

<style scoped>
/* Animação de fade para o modal */
.lumi-fade.modal-closing .modal-dialog {
  transform: translate(0, -25px);
  transition: transform 0.3s ease-out;
}

.lumi-fade.modal-closing {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}
</style>

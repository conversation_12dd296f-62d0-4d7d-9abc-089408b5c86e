/**
 * Helper para gerar URLs corretas de pacientes
 * Considera se o paciente é da mesma clínica do usuário logado ou não
 */

import { jwtDecode } from 'jwt-decode';

/**
 * Gera a URL correta para acessar um paciente
 * Funciona tanto com objetos de paciente quanto com drafts
 * @param {Object} patientOrDraft - Objeto do paciente ou draft
 * @param {number} patientOrDraft.id_ficha - ID da ficha do paciente (paciente)
 * @param {number} patientOrDraft.idFicha - ID da ficha do paciente (draft)
 * @param {number} patientOrDraft.id - ID do paciente (fallback)
 * @param {Object} patientOrDraft.clinica - Objeto da clínica do paciente
 * @param {number} patientOrDraft.clinica.id - ID da clínica do paciente
 * @param {number} patientOrDraft.clinica_id - ID da clínica do paciente (alternativo)
 * @param {number} patientOrDraft.patientClinicId - ID da clínica do paciente (draft)
 * @returns {string} URL do paciente
 */
export function getPacienteUrl(patientOrDraft) {
  // Obter usuário atual do store/localStorage
  let currentUser = null;
  try {
    // Tentar acessar do Vuex store se disponível
    if (window.app && window.app.$store) {
      currentUser = window.app.$store.state.token;
    } else {
      // Fallback para localStorage
      const token = localStorage.getItem('token');
      if (token) {
        currentUser = jwtDecode(token);
      }
    }
  } catch (error) {
    console.warn('Não foi possível obter usuário atual:', error);
  }

  // Usar id_ficha (paciente) ou idFicha (draft) se disponível, senão usar id
  const patientId = patientOrDraft.id_ficha || patientOrDraft.idFicha || patientOrDraft.id;

  if (!patientId) {
    console.error('ID do paciente não encontrado:', patientOrDraft);
    return '/pacientes';
  }

  // Obter ID da clínica do paciente (várias possibilidades)
  const patientClinicId = patientOrDraft.clinica?.id ||
                         patientOrDraft.clinica_id ||
                         patientOrDraft.patientClinicId;

  // Obter ID da clínica do usuário atual
  const currentUserClinicId = currentUser?.clinica?.id;

  // Se não há informação de clínica ou são da mesma clínica
  if (!patientClinicId || !currentUserClinicId || patientClinicId === currentUserClinicId) {
    return `/paciente/${patientId}`;
  }

  // Se é de outra clínica
  return `/paciente/${patientClinicId}/${patientId}`;
}

/**
 * Alias para getPacienteUrl para compatibilidade
 * @deprecated Use getPacienteUrl diretamente
 */
export function getPacienteUrlById(patientId, patientClinicId = null) {
  return getPacienteUrl({
    id_ficha: patientId,
    patientClinicId: patientClinicId
  });
}

/**
 * Alias para getPacienteUrl para compatibilidade com drafts
 * @deprecated Use getPacienteUrl diretamente
 */
export function getPacienteUrlForDraft(draftData) {
  return getPacienteUrl(draftData);
}

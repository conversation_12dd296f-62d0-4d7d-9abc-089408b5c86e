<template>
  <div class="w-auto h-auto collapse navbar-collapse max-height-vh-100" id="sidenav-collapse-main">
    <ul class="navbar-nav">
      <!-- Render each button group -->
      <template v-for="(group, groupIndex) in config.groups" :key="groupIndex">
        <!-- Group header -->
        <li class="mt-3 nav-item mb-1" v-if="group.title">
          <h6 class="text-xs pl-6 text-center text-uppercase font-weight-bolder text-white">
            {{ group.title }}
          </h6>
        </li>

        <!-- Group buttons -->
        <template v-for="(button, buttonIndex) in group.buttons" :key="`${groupIndex}-${buttonIndex}`">
          <li class="nav-item nav-btn-container" :class="button.class" @click="handleButtonClick(button)">
            <!-- Router link if route is provided -->
            <router-link v-if="button.route" :to="button.route" class="nav-btn" :class="button.active ? 'highlight' : ''">
              <div class="text-center d-flex align-items-center justify-content-center me-2" :class="button.iconClass">
                <!-- Material icon -->
                <i v-if="button.iconType === 'material'" class="material-icons-round fs-5">{{ button.icon }}</i>
                <!-- Font awesome icon -->
                <font-awesome-icon v-else-if="button.iconType === 'font-awesome'" :icon="button.icon" />
                <!-- Vuetify icon -->
                <v-icon v-else-if="button.iconType === 'vuetify'">{{ button.icon }}</v-icon>
              </div>
              <span :class="button.textClass">{{ button.text }}</span>
            </router-link>

            <!-- Regular link if no route -->
            <a v-else href="#" class="nav-btn" :class="button.active ? 'highlight' : ''"
              v-bind="button.attributes">
              <div class="text-center d-flex align-items-center justify-content-center me-2" :class="button.iconClass">
                <!-- Material icon -->
                <i v-if="button.iconType === 'material'" class="material-icons-round fs-5">{{ button.icon }}</i>
                <!-- Font awesome icon -->
                <font-awesome-icon v-else-if="button.iconType === 'font-awesome'" :icon="button.icon" />
                <!-- Vuetify icon -->
                <v-icon v-else-if="button.iconType === 'vuetify'">{{ button.icon }}</v-icon>
              </div>
              <span :class="button.textClass">{{ button.text }}</span>
            </a>
          </li>
        </template>

        <!-- Divider after group (if not the last group) -->
        <div v-if="groupIndex < config.groups.length - 1" class="mt-2"
          style="width: 100%; height: 1px; background: linear-gradient(90deg, #fbfdfe, #c7d7e0, #fbfdfe) !important;">
        </div>
      </template>
    </ul>
    <div class="sidenav-footer position-absolute w-100 bottom-0 pl-6 text-center">
      <img :src="lumiLogo" class="navbar-brand-img lumi-sidenav-footer-logo center-self mt-6" alt="main_logo" />
    </div>
  </div>
</template>

<script>
import lumiLogo from "@/assets/img/lumi/lumi-vision-logo-170.png";

export default {
  name: "LumiSidenavConfig",
  props: {
    /**
     * Configuration object for the sidenav
     * @example
     * {
     *   groups: [
     *     {
     *       title: "AGENDA", // Group title (optional)
     *       buttons: [
     *         {
     *           text: "Nova consulta", // Button text
     *           icon: "add", // Icon name
     *           iconType: "material", // Icon type: "material", "font-awesome", "vuetify"
     *           action: "newAppointment", // Action identifier (optional)
     *           route: "/appointment/new", // Vue router path (optional)
     *           attributes: { // HTML attributes (optional)
     *             "data-bs-toggle": "modal",
     *             "data-bs-target": "#modalNovaConsulta"
     *           },
     *           active: false, // Whether the button is active (optional)
     *           autoCollapse: true, // Whether to auto-collapse sidenav after action (default: true, except for confirmation actions)
     *           class: "", // Additional CSS classes for the li element (optional)
     *           iconClass: "", // Additional CSS classes for the icon container (optional)
     *           textClass: "" // Additional CSS classes for the text (optional)
     *         }
     *       ]
     *     }
     *   ]
     * }
     */
    config: {
      type: Object,
      required: true,
      default: () => ({
        groups: []
      })
    }
  },
  data() {
    return {
      lumiLogo
    };
  },
  methods: {
    /**
     * Handle button click
     * @param {Object} button - Button configuration
     */
    handleButtonClick(button) {
      if (button.action) {
        this.$emit('action', button.action, button);
      }
    }
  }
};
</script>

<template>
  <div class="dente-selector">
    <label class="form-label">Dentes Relacionados: <small class="text-muted">clique nos dentes para selecionar</small></label>
    <div class="dentes-grid">
      <!-- Quadrante Superior Direito -->
      <div class="quadrante">
        <div class="quadrante-label">Superior Direito</div>
        <div class="dentes-row">
          <button
            v-for="dente in [18, 17, 16, 15, 14, 13, 12, 11]"
            :key="dente"
            type="button"
            class="dente-btn"
            :class="{ 'selected': selectedDentes.includes(dente) }"
            @click="toggleDente(dente)"
          >
            {{ dente }}
          </button>
        </div>
      </div>

      <!-- Quadrante Superior Esquerdo -->
      <div class="quadrante">
        <div class="quadrante-label">Superior Esquerdo</div>
        <div class="dentes-row">
          <button
            v-for="dente in [21, 22, 23, 24, 25, 26, 27, 28]"
            :key="dente"
            type="button"
            class="dente-btn"
            :class="{ 'selected': selectedDentes.includes(dente) }"
            @click="toggleDente(dente)"
          >
            {{ dente }}
          </button>
        </div>
      </div>

      <!-- Quadrante Inferior Direito -->
      <div class="quadrante">
        <div class="quadrante-label">Inferior Direito</div>
        <div class="dentes-row">
          <button
            v-for="dente in [48, 47, 46, 45, 44, 43, 42, 41]"
            :key="dente"
            type="button"
            class="dente-btn"
            :class="{ 'selected': selectedDentes.includes(dente) }"
            @click="toggleDente(dente)"
          >
            {{ dente }}
          </button>
        </div>
      </div>

      <!-- Quadrante Inferior Esquerdo -->
      <div class="quadrante">
        <div class="quadrante-label">Inferior Esquerdo</div>
        <div class="dentes-row">
          <button
            v-for="dente in [31, 32, 33, 34, 35, 36, 37, 38]"
            :key="dente"
            type="button"
            class="dente-btn"
            :class="{ 'selected': selectedDentes.includes(dente) }"
            @click="toggleDente(dente)"
          >
            {{ dente }}
          </button>
        </div>
      </div>
    </div>

    <!-- Botões de Ação Rápida -->
    <div class="dentes-actions mt-2">
      <button type="button" class="btn btn-outline-primary me-2 dentes-action-btn" @click="selectAll">
        Selecionar Todos
      </button>
      <button type="button" class="btn btn-outline-secondary dentes-action-btn" @click="clearAll">
        Limpar Seleção
      </button>
    </div>

    <!-- Dentes Selecionados -->
    <div v-if="selectedDentes.length > 0" class="selected-dentes mt-2">
      <small class="text-muted">Dentes selecionados: {{ selectedDentes.sort((a, b) => a - b).join(', ') }}</small>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DenteSelector',
  props: {
    modelValue: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:modelValue'],
  computed: {
    selectedDentes: {
      get() {
        return this.modelValue || [];
      },
      set(value) {
        this.$emit('update:modelValue', value);
      }
    }
  },
  methods: {
    toggleDente(dente) {
      const selected = [...this.selectedDentes];
      const index = selected.indexOf(dente);
      
      if (index > -1) {
        selected.splice(index, 1);
      } else {
        selected.push(dente);
      }
      
      this.selectedDentes = selected;
    },

    selectAll() {
      const allDentes = [
        // Superior Direito
        18, 17, 16, 15, 14, 13, 12, 11,
        // Superior Esquerdo
        21, 22, 23, 24, 25, 26, 27, 28,
        // Inferior Direito
        48, 47, 46, 45, 44, 43, 42, 41,
        // Inferior Esquerdo
        31, 32, 33, 34, 35, 36, 37, 38
      ];
      this.selectedDentes = allDentes;
    },

    clearAll() {
      this.selectedDentes = [];
    }
  }
};
</script>

<style scoped>
.dente-selector {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  background-color: #f8f9fa;
}

.dentes-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 15px;
}

.quadrante {
  text-align: center;
}

.quadrante-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #6c757d;
  margin-bottom: 8px;
}

.dentes-row {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
}

.dente-btn {
  width: 35px;
  height: 35px;
  border: 2px solid #dee2e6;
  background-color: white;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dente-btn:hover {
  border-color: #007bff;
  background-color: #e7f3ff;
}

.dente-btn.selected {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.dentes-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.selected-dentes {
  text-align: center;
  padding: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
}

/* Botões responsivos */
.dentes-action-btn {
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
}

@media (max-width: 1199px) {
  .dentes-action-btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}

@media (max-width: 768px) {
  .dentes-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .dente-btn {
    width: 30px;
    height: 30px;
    font-size: 0.7rem;
  }
}
</style>

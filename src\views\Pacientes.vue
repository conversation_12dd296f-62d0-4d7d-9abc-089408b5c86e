<template>
  <lumi-sidenav
    :custom_class="color"
    icon="mdi-account-details"
    class="fixed-end lumi-sidenav"
    v-if="showSidenav"
    :config="sidenavConfig"
    @action="handleSidenavAction"
  ></lumi-sidenav>

  <div class="main-page-content">
    <!-- Lista de Pacientes -->
    <div class="row">
      <div class="col-12">
        <!-- Painel de Busca Robusto -->
        <div class="search-panel">
          <div class="search-panel-content">
            <div class="row g-3">
              <!-- Busca de Pacientes -->
              <div class="col-md-8">
                <div class="search-group">
                  <label class="search-label">
                    <i class="fas fa-user me-2"></i>
                    Buscar Pacientes
                  </label>
                  <div class="search-input-wrapper">
                    <input
                      type="text"
                      class="form-control search-input-modern"
                      placeholder="Nome, ficha ou CPF do paciente..."
                      v-model="filtros.paciente"
                      @input="aplicarFiltros"
                    />
                    <i class="fas fa-search search-icon"></i>
                    <button
                      v-if="filtros.paciente"
                      class="clear-search-btn"
                      @click="limparFiltroPaciente"
                      type="button"
                    >
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Busca de Ortodontistas -->
              <div class="col-md-4">
                <div class="search-group">
                  <label class="search-label">
                    <i class="fas fa-tooth me-2"></i>
                    Ortodontista
                  </label>
                  <div class="position-relative">
                    <div class="search-input-wrapper">
                      <input
                        type="text"
                        class="form-control search-input-modern"
                        placeholder="Selecionar ortodontista..."
                        v-model="ortodontistaSearch"
                        @focus="showOrtodontistasDropdown = true"
                        @blur="handleOrtodontistasBlur"
                        @input="filtrarOrtodontistas"
                        autocomplete="off"
                      />
                      <i v-if="!filtros.ortodontista_id" class="fas fa-chevron-down dropdown-icon"></i>
                      <button
                        v-if="filtros.ortodontista_id"
                        class="clear-search-btn"
                        @click="limparFiltroOrtodontista"
                        type="button"
                      >
                        <i class="fas fa-times"></i>
                      </button>
                    </div>

                    <!-- Dropdown de Ortodontistas -->
                    <div
                      v-show="showOrtodontistasDropdown"
                      class="ortodontistas-dropdown"
                      :class="{ 'show': showOrtodontistasDropdown }"
                    >
                      <div class="ortodontistas-dropdown-content">
                        <div
                          v-for="ortodontista in filteredOrtodontistas"
                          :key="ortodontista.id"
                          class="ortodontista-item"
                          @click="selecionarOrtodontista(ortodontista)"
                        >
                          <div class="ortodontista-nome">{{ ortodontista.nome }}</div>
                        </div>
                        <div
                          v-if="filteredOrtodontistas.length === 0 && ortodontistaSearch.trim() !== ''"
                          class="ortodontista-item no-results"
                        >
                          <div class="text-muted">Nenhum ortodontista encontrado</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="isLoading.pacientesList" class="w-100 text-center py-5">
          <div class="spinner-border text-primary" role="status"></div>
        </div>

        <v-table v-if="!isLoading.pacientesList && pacientes.length == 0" class="m-3">
          <tbody>
            <tr>
              <td
                class="bg-gradient-light text-dark text-center"
                style="border-radius: 3px; padding: 2px 20px"
              >
                <span v-if="!temFiltrosAplicados">{{ $t('patients.emptyState.noPatients') }}</span>
                <span v-if="temFiltrosAplicados">{{ $t('patients.emptyState.noResults') }}</span>
              </td>
            </tr>
          </tbody>
        </v-table>

        <EasyDataTable
          v-if="pacientes.length > 0"
          :headers="headersFiltered"
          :items="pacientes"
          @click-row="openPaciente"
          body-row-class-name="clickable"
          header-item-class-name="table-header-item"
          body-item-class-name="table-body-item"
          :rowsPerPageMessage="$t('patients.table.pagination.patientsPerPage')"
          :rowsOfPageSeparatorMessage="$t('patients.table.pagination.of')"
          :emptyMessage="$t('patients.table.pagination.noResults')"
        >
          <template #header-status="header">
            <div class="text-center w-100">{{ $t('patients.table.headers.status') }}</div>
          </template>
          <template #header-dentista="header">
            <div class="text-center w-100 p-0">{{ $t('patients.table.headers.dentist') }}</div>
          </template>

          <template #item-nome="{ nome, data_nascimento, profile_picture_url }">
            <div class="d-flex px-2 py-1">
              <div style="min-width: 40px" class="d-none d-md-block">
                <img :src="profile_picture_url" class="avatar avatar-sm me-3" alt="user1" />
              </div>
              <div class="flex-column justify-content-center">
                <h6 class="mb-0 text-sm">{{ nome }}</h6>
                <p class="d-none d-md-flex text-xs text-bold mb-0">
                  {{ $filters.howMuchTime(data_nascimento, { prefix: false }) }}
                </p>
              </div>
            </div>
          </template>

          <template #item-data_proxima_consulta="{ proxima_consulta }">
            <div class="d-flex flex-column justify-content-center">
              <p class="text-xs font-weight-bold mb-0">
                {{ $filters.dateDmy(proxima_consulta) }}
              </p>
              <p class="text-xs mb-0">
                <b>{{ $filters.howMuchTime(proxima_consulta) }}</b>
              </p>
            </div>
          </template>

          <template #item-created_at="{ created_at }">
            <div class="d-flex flex-column justify-content-center">
              <p class="text-xs font-weight-bold mb-0">{{ $filters.dateDmy(created_at) }}</p>
              <p class="text-xs mb-0">
                <b>{{ $filters.howMuchTime(created_at, { type: 'date' }) }}</b>
              </p>
            </div>
          </template>

          <template
            #item-status="{ status_tratamento, data_inicio_tratamento, data_final_prevista }"
          >
            <div class="align-middle text-center pe-3">
              <span
                class="badge badge-sm w-100 w-md-70"
                :class="statusClass(status_tratamento)"
                v-if="status_tratamento !== 'ATIVO'"
                >{{ statusText(status_tratamento) }}</span
              >

              <div
                class="d-flex flex-column align-items-center justify-content-center"
                v-if="status_tratamento === 'ATIVO'"
              >
                <div class="progress progress-sm w-100 w-md-70">
                  <div :style="{width: getProgresso(data_inicio_tratamento, data_final_prevista) + '%'}">
                    <div
                      class="progress-bar bg-gradient-success"
                      role="progressbar"
                      :aria-valuenow="0"
                      aria-valuemin="0"
                      aria-valuemax="100"
                    ></div>
                  </div>
                  <span
                    class="progress-value"
                    >{{ getProgresso(data_inicio_tratamento, data_final_prevista) }}%</span>
                </div>
              </div>
            </div>
          </template>

          <template #item-dentista="{ dentista }">
            <div class="w-100 text-center pe-3">
              <span class="text-xs text-dark font-weight-bold text-uppercase">{{ dentista }}</span>
            </div>
          </template>
        </EasyDataTable>
      </div>
    </div>


  </div>



  <div class="modal" tabindex="-1" id="modalAdicionarPacienteDoFormulario">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{{ $t('patients.modals.addPatient.title') }}</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
          ></button>
        </div>
        <div class="modal-body py-4">
          <div class="d-flex flex-column">
            <button class="btn btn-secondary my-3">
              <i class="fas fa-user me-2"></i>
              {{ $t('patients.modals.addPatient.linkExisting') }}
            </button>
            <button
              class="btn btn-primary my-3"
              data-bs-toggle="modal"
              data-bs-target="#modalNovoPaciente"
            >
              <i class="fas fa-plus me-2"></i>
              {{ $t('patients.modals.addPatient.createNew') }}
            </button>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-bs-dismiss="modal">
            {{ $t('patients.modals.addPatient.back') }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade lumi-fade" tabindex="-1" id="modalNovoPaciente" ref="modalNovoPaciente">
    <div class="modal-dialog modal-dialog-centered modal-lg">
      <div class="modal-content modern-modal">
        <div class="modal-header modern-header">
          <h5 class="modal-title">
            <i class="fas fa-user-plus me-2"></i>
            {{ $t('patients.modals.newPatient.title') }}
          </h5>
          <button
            type="button"
            class="btn-close btn-close-white"
            data-bs-dismiss="modal"
            aria-label="Close"
            ref="closeModalNovoPaciente"
          ></button>
        </div>
        <div class="modal-body modern-body">
          <!-- Seção: Informações Pessoais -->
          <div class="form-section">
            <h6 class="section-title">
              <i class="fas fa-user me-2"></i>
              Informações Pessoais
            </h6>
            <div class="row">
              <div class="col-12">
                <div class="form-group-modern">
                  <label class="form-label-modern">
                    {{ $t('patients.modals.newPatient.fields.name') }}
                  </label>
                  <MaterialInput
                    type="text"
                    class="form-control form-control-modern"
                    v-model="novoPaciente.nome"
                    ref="nome"
                    :input="
                      function ($event) {
                        // Remover números e caracteres especiais, manter apenas letras e espaços
                        $event.target.value = $event.target.value.replace(/[^a-zA-ZÀ-ÿ\s]/g, '');
                        capitalizeAll($event);
                      }
                    "
                  />
                </div>
              </div>

              <div class="col-sm-6">
                <div class="form-group-modern">
                  <label class="form-label-modern">
                    {{ $t('patients.modals.newPatient.fields.phone') }}
                  </label>
                  <MaterialInput
                    type="text"
                    class="form-control-modern"
                    label=""
                    v-model="novoPaciente.celular"
                    :mask="phoneMaskWrapper(novoPaciente.celular)"
                    placeholder="(##) #####-####"
                  />
                  <div class="checkbox-modern mt-2">
                    <input
                      type="checkbox"
                      id="novo-paciente-celular-whatsapp"
                      class="checkbox-input"
                      v-model="novoPaciente.celular_whatsapp"
                    />
                    <label for="novo-paciente-celular-whatsapp" class="checkbox-label">
                      <i class="fab fa-whatsapp me-1"></i>
                      WhatsApp
                    </label>
                  </div>
                </div>
              </div>

              <div class="col-sm-6">
                <div class="form-group-modern">
                  <label class="form-label-modern">
                    {{ $t('patients.modals.newPatient.fields.language') }}
                  </label>
                  <select class="form-select form-control-modern" v-model="novoPaciente.idioma">
                    <option value="pt">Português</option>
                    <option value="en">Inglês</option>
                    <option value="es">Espanhol</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <!-- Seção: Clínica e Ortodontista -->
          <div class="form-section">
            <h6 class="section-title">
              <i class="fas fa-hospital me-2"></i>
              Clínica e Ortodontista
            </h6>
            <div class="row">
              <div class="col-sm-6">
                <div class="form-group-modern">
                  <label class="form-label-modern">
                    {{ $t('patients.modals.newPatient.fields.clinic') }}
                  </label>

                  <select
                    v-if="$user.system_admin && novoPaciente.clinica_id !== 'add'"
                    class="form-select form-control-modern"
                    aria-label="Default select example"
                    v-model="novoPaciente.clinica_id"
                    @change="changeClinica"
                  >
                    <option hidden selected value="">{{ $t('patients.modals.newPatient.fields.select') }}</option>
                    <option v-for="clinica in clinicas" :key="clinica.id" :value="clinica.id">
                      {{ clinica.nome }}
                    </option>
                  </select>

                  <MaterialInput
                    v-if="$user.system_admin && novoPaciente.clinica_id == 'add'"
                    type="text"
                    class="form-control-modern"
                    :placeholder="$t('patients.modals.newPatient.fields.newClinicPlaceholder')"
                    ref="novaClinica"
                    :input="
                      function ($event) {
                        capitalizeAll($event);
                      }
                    "
                    v-model="novoPaciente.novaClinica"
                  />

                  <input
                      v-if="!$user.system_admin"
                      readonly
                      class="form-control form-control-modern"
                      type="text"
                      :value="$clinica?.nome ? $clinica.nome : ''"
                    />
                </div>
              </div>

              <div class="col-sm-6">
                <div class="form-group-modern">
                  <label class="form-label-modern">
                    {{ $t('patients.modals.newPatient.fields.dentist') }}
                  </label>

                  <select
                    v-if="novoPaciente.dentista_id !== 'add'"
                    class="form-select form-control-modern"
                    aria-label="Default select example"
                    v-model="novoPaciente.dentista_id"
                  >
                    <option hidden selected value="">{{ $t('patients.modals.newPatient.fields.select') }}</option>
                    <option
                      v-for="dentista in dentistasFiltrados"
                      :key="dentista.id"
                      :value="dentista.id"
                    >
                      {{ dentista.nome }}
                    </option>
                  </select>
                </div>
              </div>
            </div>
          </div>



          <!-- Seção: Observações -->
          <div class="form-section">
            <h6 class="section-title">
              <i class="fas fa-sticky-note me-2"></i>
              Observações
            </h6>
            <div class="row">
              <div class="col-12">
                <div class="form-group-modern">
                  <textarea
                    class="form-control form-control-modern"
                    rows="4"
                    v-model="novoPaciente.observacoes"
                    placeholder="Observações adicionais sobre o paciente..."
                  ></textarea>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer modern-footer">
          <button type="button" class="btn btn-primary btn-modern" @click="_addNovoPaciente">
            <i class="fas fa-plus me-2"></i>
            {{ $t('patients.modals.newPatient.addButton') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
const evts = [
  {
    date: "2024-05-04T14:00:00.000Z",
    comment: "",
    id: "cl32rbkjk1700101o53e3e3uhn",
    keywords: "Projet BAMBA",
    name: "MONTCHO Kévin",
  },
  //...
];

const cfg = {
  viewEvent: undefined,
  reportEvent: {
    icon: true,
    text: "",
  },
  searchPlaceholder: "",
  eventName: "",
  closeText: "",
  nativeDatepicker: false,
  todayButton: true,
  firstDayOfWeek: 1,
};

import cSwal from "@/utils/cSwal.js";
import { mapMutations, mapState } from "vuex";
import LumiSidenav from "@/views/components/LumiSidenav/index.vue";
import { getDentistas } from "@/services/dentistasService";
import { getClinicas } from "@/services/clinicasService";
import { addNovoPaciente, searchPacientes } from "@/services/pacientesService";
import { phoneMask, capitalizeAll } from "@/helpers/utils.js";
import { closeModalWithAnimation } from "@/utils/modalHelper.js";
import { getPacienteUrl } from "@/helpers/patientUrlHelper";
import MaterialInput from "@/components/MaterialInput.vue";

const headers = [
  { text: "PACIENTE", value: "nome", sortable: true },
  { text: "PRÓXIMA CONSULTA", value: "data_proxima_consulta", sortable: true },
  { text: "STATUS DO TRATAMENTO", value: "status", sortable: true, align: "center" },
  { text: "ORTODONTISTA", value: "dentista", sortable: true },
  { text: "CADASTRADO EM", value: "created_at", sortable: true },
];



var nomeNovoPaciente = "";

var pacientes = [];

var search = "";

var novoPaciente = getNovoPaciente();

function getNovoPaciente() {
  return {
    idioma: "pt",
    clinica_id: "",
    dentista_id: "",
    nome: "",
    celular: "",
    celular_whatsapp: true,
    novaClinica: "",
  };
}

export default {
  name: "tables",
  components: {
    MaterialInput,
    LumiSidenav,
  },
  async created() {
    this.refreshClinicas();
    this.refreshDentistas();
    this.updateList();
  },



  mounted() {
    // Configurar a sidenav
    this.updateSidenavConfig();

    // Configurar eventos do modal
    const modalElement = document.getElementById('modalNovoPaciente');
    if (modalElement) {
      // Focar no campo de nome quando o modal for aberto
      modalElement.addEventListener("shown.bs.modal", (event) => {
        if (this.$refs.nome) {
          this.$refs.nome.getInput().focus();
        }
      });

      // Resetar o formulário quando o modal for fechado
      modalElement.addEventListener("hidden.bs.modal", (event) => {
        this.resetFormOnModalClose();
        // Remover a classe de fechamento
        modalElement.classList.remove('modal-closing');
      });

      // Adicionar classe para animação de fechamento
      modalElement.addEventListener('hide.bs.modal', () => {
        modalElement.classList.add('modal-closing');
      });

      // Fechar sidenav quando o modal for aberto em telas pequenas
      modalElement.addEventListener('show.bs.modal', () => {
        // Verificar se estamos em uma tela pequena (< 992px - breakpoint md do Bootstrap)
        if (window.innerWidth < 992) {
          this.closeSidenav();
        }
      });
    }

    // Adicionar listener para mudanças no tamanho da tela
    this.handleResize = () => {
      this.screenWidth = window.innerWidth;
    };
    window.addEventListener('resize', this.handleResize);
  },

  beforeUnmount() {
    // Remover event listeners ao destruir o componente
    document.removeEventListener('click', this.handleClickOutsideOrtodontistas);
    window.removeEventListener('resize', this.handleResize);
  },

  methods: {

    ...mapMutations(["navbarMinimize"]),

    // Método para fechar a sidenav
    closeSidenav() {
      // Verificar se a sidenav está aberta
      const sidenavElement = document.querySelector(".g-sidenav-show");
      if (sidenavElement && sidenavElement.classList.contains("g-sidenav-pinned")) {
        // Usar o método do Vuex para fechar a sidenav
        this.navbarMinimize();
      }
    },

    updateSidenavConfig() {
      this.sidenavConfig = {
        groups: [
          {
            title: "PACIENTES",
            buttons: [
              {
                text: "Lista de Pacientes",
                icon: ["fas", "users"],
                iconType: "font-awesome",
                action: "listPatients",
                active: true
              },
              {
                text: this.$t('patients.sidenav.newPatient'),
                icon: "add",
                iconType: "material",
                action: "newPatient",
                attributes: {
                  "data-bs-toggle": "modal",
                  "data-bs-target": "#modalNovoPaciente"
                }
              }
            ]
          }
        ]
      };
    },

    handleSidenavAction(action, button) {
      console.log(`Action: ${action}`, button);

      // Implementar as ações da sidenav
      switch (action) {
        case 'newPatient':
          // Modal já é aberto automaticamente pelos atributos data-bs-*
          break;
      }
    },
    async refreshClinicas() {
      if (!this.$user.system_admin)
        return;
      this.clinicas = await getClinicas();
      if (this.$clinica) {
        this.novoPaciente.clinica_id = this.$clinica.id;
      } else if (this.clinicas.length > 0) {
        this.novoPaciente.clinica_id = this.clinicas[0].id;
      }
    },
    async refreshDentistas() {
      this.dentistas = await getDentistas();
      // Verificar se dentistas foi carregado corretamente
      if (!this.dentistas || !Array.isArray(this.dentistas)) {
        this.dentistas = [];
        return;
      }

      // Inicializar lista filtrada de ortodontistas
      this.filteredOrtodontistas = [...this.dentistas];

      if (this.$dentista) {
        this.novoPaciente.dentista_id = this.$dentista.id;
      } else if (this.dentistas.length > 0) {
        this.novoPaciente.dentista_id = this.dentistas[0].id;
      }
    },
    capitalizeAll,
    changeClinica() {
      if (this.novoPaciente.clinica_id == "add") {
        this.$refs.novaClinica.getInput().focus();
      } else {
        // Selecionar automaticamente o primeiro dentista da clínica selecionada
        const dentistasDaClinica = this.dentistasFiltrados;
        if (dentistasDaClinica && Array.isArray(dentistasDaClinica) && dentistasDaClinica.length > 0) {
          this.novoPaciente.dentista_id = dentistasDaClinica[0].id;
        } else {
          this.novoPaciente.dentista_id = "";
        }
      }
    },

    /**
     * Wraps the phoneMask function to pass the length parameter to it.
     * @param {number} length The length of the phone number.
     * @returns {import('vue-the-mask').MaskPattern} The mask object.
     */
    phoneMaskWrapper(length) {
      return phoneMask(length);
    },

    async updateList(search = "") {
      this.isLoading.pacientesList = true;
      // Se não há busca específica, carregar todos os pacientes
      const resultado = await searchPacientes(search);
      this.pacientesOriginais = resultado;

      // Aplicar filtros se existirem
      const temFiltros = this.filtros.paciente.trim() !== '' || this.filtros.ortodontista_id !== '';
      if (!temFiltros) {
        this.pacientes = resultado;
      } else {
        this.aplicarFiltros();
      }

      this.isLoading.pacientesList = false;
    },

    // Métodos para o painel de busca
    aplicarFiltros() {
      let pacientesFiltrados = [...this.pacientesOriginais];

      // Filtro por paciente (nome, ficha, CPF)
      if (this.filtros.paciente.trim() !== '') {
        const searchTerm = this.filtros.paciente.toLowerCase().trim();

        pacientesFiltrados = pacientesFiltrados.filter(paciente => {
          // Busca por nome
          const nomeMatch = paciente.nome && paciente.nome.toLowerCase().includes(searchTerm);

          // Busca por ID da ficha
          const fichaMatch = paciente.id_ficha && paciente.id_ficha.toString().includes(searchTerm);

          // Busca por CPF (removendo formatação)
          let cpfMatch = false;
          if (paciente.cpf && searchTerm.replace(/\D/g, '').length > 0) {
            cpfMatch = paciente.cpf.replace(/\D/g, '').includes(searchTerm.replace(/\D/g, ''));
          }

          // Busca por número da ficha formatado (#001, #002, etc)
          const fichaFormatadaMatch = paciente.id_ficha &&
            `#${paciente.id_ficha.toString().padStart(3, '0')}`.toLowerCase().includes(searchTerm);

          return nomeMatch || fichaMatch || cpfMatch || fichaFormatadaMatch;
        });
      }

      // Filtro por ortodontista
      if (this.filtros.ortodontista_id !== '') {
        pacientesFiltrados = pacientesFiltrados.filter(paciente => {
          return paciente.dentista_id == this.filtros.ortodontista_id;
        });
      }

      this.pacientes = pacientesFiltrados;
    },

    filtrarOrtodontistas() {
      this.showOrtodontistasDropdown = true;

      if (!this.ortodontistaSearch.trim()) {
        this.filteredOrtodontistas = [...this.dentistas];
        return;
      }

      const searchTerm = this.ortodontistaSearch.toLowerCase();
      this.filteredOrtodontistas = this.dentistas.filter(ortodontista => {
        return ortodontista.nome?.toLowerCase().includes(searchTerm);
      });

      // Se o texto não corresponder ao ortodontista selecionado, limpar a seleção
      if (this.ortodontistaSelecionado &&
          this.ortodontistaSearch !== this.ortodontistaSelecionado.nome) {
        this.filtros.ortodontista_id = '';
        this.ortodontistaSelecionado = null;
        this.aplicarFiltros();
      }
    },

    selecionarOrtodontista(ortodontista) {
      this.filtros.ortodontista_id = ortodontista.id;
      this.ortodontistaSelecionado = ortodontista;
      this.ortodontistaSearch = ortodontista.nome;
      this.showOrtodontistasDropdown = false;
      this.aplicarFiltros();
    },



    handleOrtodontistasBlur() {
      // Delay para permitir clique no dropdown
      setTimeout(() => {
        this.showOrtodontistasDropdown = false;
      }, 200);
    },

    limparFiltroPaciente() {
      this.filtros.paciente = '';
      this.aplicarFiltros();
    },

    limparFiltroOrtodontista() {
      this.filtros.ortodontista_id = '';
      this.ortodontistaSelecionado = null;
      this.ortodontistaSearch = '';
      this.aplicarFiltros();
    },

    limparTodosFiltros() {
      this.filtros.paciente = '';
      this.filtros.ortodontista_id = '';
      this.ortodontistaSelecionado = null;
      this.ortodontistaSearch = '';
      this.aplicarFiltros();
    },

    handleClickOutsideOrtodontistas(event) {
      if (!this.showOrtodontistasDropdown) return;

      const dropdown = document.querySelector('.ortodontistas-dropdown');
      const inputWrapper = document.querySelector('.ortodontistas-dropdown')?.previousElementSibling;

      if (dropdown && inputWrapper &&
          !dropdown.contains(event.target) &&
          !inputWrapper.contains(event.target)) {
        this.showOrtodontistasDropdown = false;
      }
    },
    statusClass(status) {
      const classMap = {
        "NÃO INICIADO": "bg-gradient-warning",
        CONCLUÍDO: "bg-gradient-success",
        ATIVO: "bg-gradient-secondary",
      };

      return classMap[status] || "";
    },
    async _addNovoPaciente() {
      if (this.$user.system_admin && !this.novoPaciente.clinica_id) {
        cSwal.cAlert(this.$t('patients.alerts.clinicRequired'));
        return false;
      }

      cSwal.loading(this.$t('patients.alerts.addingPatient'));
      const add = await addNovoPaciente(this.novoPaciente);
      cSwal.loaded();

      if (add) {
        // Salvar dados do paciente criado e opção WhatsApp
        const pacienteCriado = {
          nome: this.novoPaciente.nome,
          celular_whatsapp: this.novoPaciente.celular_whatsapp,
          celular: this.novoPaciente.celular,
          id_ficha: add.id_ficha || add.data?.id_ficha || add.id || null,
          public_token: add.public_token || add.data?.public_token || add.token || null
        };

        // Form reset will be handled by success modal actions

        // Fechar o modal com animação
        closeModalWithAnimation('modalNovoPaciente');

        // Fechar a sidenav
        this.closeSidenav();

        // Atualizar lista antes de mostrar modal
        await this.updateList(this.search);

        // Mostrar modal de sucesso refinado
        this.showSuccessModal(pacienteCriado);
      } else {
        cSwal.cError(this.$t('patients.alerts.errorAddingPatient'));
      }

      this.refreshClinicas();
      this.updateList(this.search);
    },
    statusText(status) {
      const textMap = {
        "NÃO INICIADO": this.$t('patients.table.status.notStarted'),
        CONCLUÍDO: this.$t('patients.table.status.completed'),
        ATIVO: this.$t('patients.table.status.inProgress'),
      };

      return textMap[status] || "";
    },

    // Modal de sucesso refinado
    showSuccessModal(paciente) {
      const possuiWhatsapp = paciente.celular_whatsapp && paciente.celular;
      const linkText = possuiWhatsapp ? "Enviar Link da Ficha" : "Copiar Link da Ficha";
      const linkIcon = possuiWhatsapp ? "fab fa-whatsapp" : "fas fa-copy";

      cSwal.fire({
        html: `
          <div style="text-align: center; padding: 20px 0;">
            <h3 style="color: #28a745; font-size: 1.8rem; margin-bottom: 10px; font-weight: 600;">
              ${paciente.nome}
            </h3>
            <p style="color: #6c757d; font-size: 1rem; margin-bottom: 30px;">
              Paciente adicionado com sucesso!
            </p>

            <div style="display: flex; gap: 10px; margin-bottom: 20px; justify-content: center;">
              <button id="btn-abrir-prontuario" class="btn btn-primary" style="flex: 1; max-width: 180px;">
                <i class="fas fa-user-md me-2"></i>Abrir Prontuário
              </button>
              <button id="btn-agendar-consulta" class="btn btn-success" style="flex: 1; max-width: 180px;">
                <i class="fas fa-calendar-plus me-2"></i>Agendar Consulta
              </button>
            </div>

            <button id="btn-link-ficha" class="btn btn-outline-primary" style="width: 100%; max-width: 380px;">
              <i class="${linkIcon} me-2"></i>${linkText}
            </button>

            <button id="btn-fechar-modal" class="btn btn-link text-muted" style="width: 100%; max-width: 380px; margin-top: 20px; font-size: 0.9rem; text-decoration: none;">
              <i class="fas fa-times me-2"></i>Fechar
            </button>
          </div>
        `,
        icon: 'success',
        showConfirmButton: false,
        showCancelButton: false,
        allowOutsideClick: true,
        allowEscapeKey: true,
        customClass: {
          popup: 'swal2-success-modal'
        },
        willClose: () => {
          // Reset form when success modal is closed without action
          this.resetFormAfterSuccess();
        },
        didOpen: () => {
          // Adicionar event listeners aos botões
          const btnProntuario = document.getElementById('btn-abrir-prontuario');
          const btnConsulta = document.getElementById('btn-agendar-consulta');
          const btnLink = document.getElementById('btn-link-ficha');
          const btnFechar = document.getElementById('btn-fechar-modal');

          if (btnProntuario) {
            btnProntuario.addEventListener('click', () => {
              this.abrirProntuario(paciente);
              this.resetFormAfterSuccess();
              cSwal.close();
            });
          }

          if (btnConsulta) {
            btnConsulta.addEventListener('click', () => {
              this.agendarConsulta(paciente);
              this.resetFormAfterSuccess();
              cSwal.close();
            });
          }

          if (btnLink) {
            btnLink.addEventListener('click', () => {
              this.handleLinkFicha(paciente);
              this.resetFormAfterSuccess();
              // Não fechar o modal aqui - deixar aberto para o usuário ver o feedback
            });
          }

          if (btnFechar) {
            btnFechar.addEventListener('click', () => {
              this.resetFormAfterSuccess();
              cSwal.close();
            });
          }
        }
      });
    },

    // Abrir prontuário do paciente
    abrirProntuario(paciente) {
      if (paciente.id_ficha) {
        // Usar helper para gerar URL correta
        const targetUrl = getPacienteUrl(paciente);
        console.log('targetUrl', targetUrl)
        this.$router.push(targetUrl);
      } else {
        // Tentar encontrar o paciente na lista atualizada
        const pacienteEncontrado = this.pacientes.find(p =>
          p.nome.toLowerCase() === paciente.nome.toLowerCase()
        );

        if (pacienteEncontrado && pacienteEncontrado.id_ficha) {
          // Usar helper para gerar URL correta
          const targetUrl = getPacienteUrl(pacienteEncontrado);
          this.$router.push(targetUrl);
        } else {
          this.showToast("Paciente criado com sucesso! Atualize a página para acessar o prontuário.", 'info');
        }
      }
    },

    // Agendar consulta
    agendarConsulta(paciente) {
      // Navegar para a página de agenda
      this.$router.push({
        name: "Agenda"
      });
      this.showToast("Redirecionando para a agenda...", 'info');
    },

    // Lidar com link da ficha (WhatsApp ou copiar)
    async handleLinkFicha(paciente) {
      if (!paciente.public_token) {
        // Tentar encontrar o paciente na lista atualizada para obter o token
        const pacienteEncontrado = this.pacientes.find(p =>
          p.nome.toLowerCase() === paciente.nome.toLowerCase()
        );

        if (pacienteEncontrado && pacienteEncontrado.public_token) {
          paciente.public_token = pacienteEncontrado.public_token;
        } else {
          this.showToast("Link da ficha será disponibilizado em breve. Paciente criado com sucesso!", 'info');
          return;
        }
      }

      if (paciente.celular_whatsapp && paciente.celular) {
        this.enviarLinkWhatsApp(paciente);
      } else {
        await this.copiarLinkFicha(paciente);
      }
    },

    // Enviar link via WhatsApp
    enviarLinkWhatsApp(paciente) {
      const phoneNumber = paciente.celular.replace(/\D+/g, "");
      if (phoneNumber.length !== 11) {
        this.showToast("Número de WhatsApp inválido. Por favor, verifique o número.", 'error');
        return;
      }

      const link = this.getFichaInicialLink(paciente.public_token);
      const whatsappLink = `https://wa.me/55${phoneNumber}?text=Olá, bem-vindo à clínica! Por favor, preencha nosso formulário para lhe conhecermos melhor: ${link}`;
      window.open(whatsappLink, "_blank");

      // Não fechar o modal - apenas mostrar feedback
      this.showToast("Link enviado via WhatsApp!", 'success');
    },

    // Copiar link da ficha
    async copiarLinkFicha(paciente) {
      const link = this.getFichaInicialLink(paciente.public_token);

      if (!navigator.clipboard) {
        // Fallback: mostrar modal com link (este modal substitui o atual)
        cSwal.fire({
          title: 'Link da Ficha de Avaliação',
          html: `
            <p>Link para o paciente <strong>${paciente.nome}</strong>:</p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; word-break: break-all;">
              <strong>${link}</strong>
            </div>
            <p><small>Copie o link acima e envie para o paciente.</small></p>
          `,
          icon: 'info',
          confirmButtonText: 'Entendi',
          customClass: {
            popup: 'swal2-link-modal'
          }
        });
        return;
      }

      try {
        await navigator.clipboard.writeText(link);
        // Não fechar o modal - apenas mostrar feedback
        this.showToast("Link copiado para a área de transferência!", 'success');
      } catch (error) {
        console.error("Erro ao copiar link:", error);
        this.showToast("Erro ao copiar link. Tente novamente.", 'error');
      }
    },

    // Gerar link da ficha inicial
    getFichaInicialLink(publicToken) {
      return `${window.location.origin}/bem-vindo/?t=${publicToken}`;
    },

    // Reset form after successful action in success modal
    resetFormAfterSuccess() {
      // Get current clinic selection to preserve it
      const currentClinicId = this.novoPaciente.clinica_id;

      // Reset form to initial state
      this.novoPaciente = getNovoPaciente();

      // Preserve clinic selection for system admin
      if (this.$user.system_admin && currentClinicId) {
        this.novoPaciente.clinica_id = currentClinicId;
      }
    },

    // Reset form when modal is manually closed
    resetFormOnModalClose() {
      // Get current clinic selection to preserve it
      const currentClinicId = this.novoPaciente.clinica_id;

      // Reset form to initial state
      this.novoPaciente = getNovoPaciente();

      // Preserve clinic selection for system admin
      if (this.$user.system_admin && currentClinicId) {
        this.novoPaciente.clinica_id = currentClinicId;
      }
    },

    // Sistema simples de toast notifications
    showToast(message, type = 'info') {
      // Criar elemento do toast
      const toastId = 'toast-' + Date.now();
      const toastHtml = `
        <div id="${toastId}" class="toast fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
          <div class="toast-header bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} text-white">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
            <strong class="me-auto">${type === 'success' ? 'Sucesso' : type === 'error' ? 'Erro' : 'Informação'}</strong>
            <button type="button" class="btn-close btn-close-white" onclick="document.getElementById('${toastId}').remove()"></button>
          </div>
          <div class="toast-body text-dark">
            ${message}
          </div>
        </div>
      `;

      // Adicionar ao DOM
      document.body.insertAdjacentHTML('beforeend', toastHtml);

      // Remover automaticamente após 4 segundos
      setTimeout(() => {
        const toastElement = document.getElementById(toastId);
        if (toastElement) {
          toastElement.classList.remove('show');
          setTimeout(() => toastElement.remove(), 300);
        }
      }, 4000);
    },
    openPaciente(paciente) {
      // Usar helper para gerar URL correta, considerando a clínica do paciente
      const targetUrl = getPacienteUrl(paciente);
      this.$router.push(targetUrl);
    },

    getProgresso(data_inicio_tratamento, data_final_prevista) {
      if (!data_inicio_tratamento || !data_final_prevista) return "-";

      const inicio = new Date(data_inicio_tratamento);
      const termino = new Date(data_final_prevista);
      const hoje = new Date();

      if (hoje < inicio) return 0.0;
      if (hoje > termino) return 100.0;

      const duracaoTotal = termino.getTime() - inicio.getTime();
      const duracaoAteHoje = hoje.getTime() - inicio.getTime();

      const progresso = (duracaoAteHoje / duracaoTotal) * 100;

      return parseFloat(progresso.toFixed(1));
    },
  },

  data() {
    return {
      clinicas: [],
      dentistas: [],
      isLoading: {
        pacientesList: true,
      },
      nomeNovoPaciente,
      headers,
      cfg,
      evts,
      pacientes,
      pacientesOriginais: [], // Lista original sem filtros
      search,
      novoPaciente,
      sidenavConfig: null,
      // Novos dados para o painel de busca
      filtros: {
        paciente: '',
        ortodontista_id: ''
      },
      ortodontistaSearch: '',
      showOrtodontistasDropdown: false,
      filteredOrtodontistas: [],
      ortodontistaSelecionado: null,
      // Controle de tamanho da tela
      screenWidth: window.innerWidth
    };
  },

  computed: {
    ...mapState([
      "isRTL",
      "color",
      "isAbsolute",
      "isNavFixed",
      "navbarFixed",
      "absolute",
      "showSidenav",
      "showNavbar",
      "showFooter",
      "showConfig",
      "hideConfigButton",
    ]),

    // Filtrar dentistas pela clínica selecionada
    dentistasFiltrados() {
      // Verificar se dentistas existe e é um array
      if (!this.dentistas || !Array.isArray(this.dentistas)) {
        return [];
      }

      if (!this.novoPaciente.clinica_id || this.novoPaciente.clinica_id === "add") {
        return this.dentistas;
      }

      return this.dentistas.filter(dentista => {
        // Verificar se o dentista tem clinica_id diretamente
        if (dentista.clinica_id) {
          return dentista.clinica_id == this.novoPaciente.clinica_id;
        }

        // Verificar se o dentista tem um objeto clinica com id
        if (dentista.clinica && dentista.clinica.id) {
          return dentista.clinica.id == this.novoPaciente.clinica_id;
        }

        return false;
      });
    },

    // Verificar se há filtros aplicados
    temFiltrosAplicados() {
      return this.filtros.paciente.trim() !== '' || this.filtros.ortodontista_id !== '';
    },

    // Filtrar headers baseado no tamanho da tela
    headersFiltered() {
      // Se a tela for menor que 768px, remover a coluna "CADASTRADO EM"
      if (this.screenWidth < 768) {
        return this.headers.filter(header => header.value !== 'created_at');
      }
      return this.headers;
    }
  }
};
</script>

<style scoped>
/* Estilos para o painel de busca */
.search-panel {
  background: #FAFAFA;
  margin: 0;
  padding: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.search-panel-content {
  padding: 0.75rem 1rem;
  padding-top: 0px;
  /* background: rgba(255, 255, 255, 0.95); */
  backdrop-filter: blur(10px);
  border-radius: 0;
}

.search-group {
  margin-bottom: 0;
}

.search-label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  display: block;
}

.search-input-wrapper {
  position: relative;
}

.search-input-modern {
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-input-modern:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  outline: none;
}

.search-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  pointer-events: none;
}

.dropdown-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  pointer-events: none;
  transition: transform 0.3s ease;
}

.clear-search-btn {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6c757d;
  padding: 0.25rem;
  border-radius: 50%;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 10;
}

.clear-search-btn:hover {
  background-color: #dc3545;
  color: white;
}

/* Dropdown de ortodontistas */
.ortodontistas-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 999999;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  margin-top: 4px;
  background-color: white;
  border: 2px solid #e9ecef;
  opacity: 0;
  visibility: hidden;
}

.ortodontistas-dropdown.show {
  max-height: 250px;
  overflow-y: auto;
  opacity: 1;
  visibility: visible;
}

.ortodontistas-dropdown-content {
  padding: 0.5rem 0;
}

.ortodontista-item {
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f8f9fa;
}

.ortodontista-item:last-child {
  border-bottom: none;
}

.ortodontista-item:hover {
  background-color: rgba(102, 126, 234, 0.1);
}

.ortodontista-item.no-results {
  cursor: default;
  color: #6c757d;
  font-style: italic;
}

.ortodontista-item.no-results:hover {
  background-color: transparent;
}

.ortodontista-nome {
  font-weight: 500;
  color: #2c3e50;
}



/* Estilos para o modal moderno de adicionar paciente */
.modern-modal {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.modern-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  padding: 0.75rem 1rem;
  border-bottom: none;
}

.modern-header .modal-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
}

.modern-body {
  padding: 1rem;
  background: #f8f9fa;
}

.form-section {
  background: white;
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 0.5rem 0;
  font-size: 0.85rem;
  font-weight: 600;
  color: #007bff;
  display: flex;
  align-items: center;
  padding-bottom: 0.25rem;
  border-bottom: 1px solid #e9ecef;
}

.form-group-modern {
  margin-bottom: 0.75rem;
}

.form-group-modern:last-child {
  margin-bottom: 0;
}

.form-label-modern {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.25rem;
  display: block;
  font-size: 0.8rem;
}

.form-control-modern {
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  line-height: 1.4;
  min-height: 38px;
}

.form-control-modern:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  outline: none;
}

/* Fix para selects cortados */
.form-select.form-control-modern {
  padding: 0.5rem 2rem 0.5rem 0.75rem;
  background-position: right 0.75rem center;
  background-size: 14px 10px;
}

.form-select.form-control-modern option {
  padding: 0.4rem 0.75rem;
  line-height: 1.4;
}

.checkbox-modern {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkbox-input {
  width: 18px;
  height: 18px;
  accent-color: #007bff;
}

.checkbox-label {
  font-size: 0.9rem;
  color: #495057;
  cursor: pointer;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.modern-footer {
  background: white;
  padding: 0.75rem 1rem;
  border-top: 1px solid #e9ecef;
}

.btn-modern {
  padding: 0.5rem 1.25rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.9rem;
  background: linear-gradient(135deg, #007bff, #0056b3);
  border: none;
  transition: all 0.3s ease;
}

.btn-modern:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* Estilos para o modal de sucesso */
:global(.swal2-success-modal) {
  border-radius: 15px !important;
}

:global(.swal2-success-modal .swal2-icon.swal2-success) {
  border-color: #28a745 !important;
  color: #28a745 !important;
}

:global(.swal2-success-modal .swal2-icon.swal2-success .swal2-success-ring) {
  border-color: #28a745 !important;
}

:global(.swal2-success-modal .swal2-icon.swal2-success) {
  background-color: #28a745 !important;
}

:global(.swal2-success-modal .btn) {
  border-radius: 8px !important;
  font-weight: 500 !important;
  padding: 10px 20px !important;
  transition: all 0.2s ease !important;
}

:global(.swal2-success-modal .btn:hover) {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
}

:global(.swal2-success-modal .btn-link) {
  border: none !important;
  background: none !important;
  box-shadow: none !important;
  padding: 8px 20px !important;
}

:global(.swal2-success-modal .btn-link:hover) {
  transform: none !important;
  box-shadow: none !important;
  background-color: rgba(0,0,0,0.05) !important;
  border-radius: 6px !important;
  text-decoration: none !important;
}

:global(.swal2-link-modal) {
  border-radius: 15px !important;
}

/* Estilos para os toasts */
.toast {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
  border-radius: 8px !important;
}

.toast-header {
  border-radius: 8px 8px 0 0 !important;
}

.toast-body {
  background-color: white !important;
  border-radius: 0 0 8px 8px !important;
}

/* Responsividade para o painel de busca */
@media (max-width: 768px) {
  .search-panel-content {
    padding: 0.5rem 0.75rem;
  }

  .ortodontistas-dropdown {
    z-index: 1000000;
  }

  .search-input-modern {
    padding: 0.6rem 2.2rem 0.6rem 0.8rem;
    font-size: 0.9rem;
  }

  .ortodontistas-dropdown.show {
    max-height: 200px;
  }
}

@media (max-width: 576px) {
  .search-panel-content {
    padding: 0.4rem 0.5rem;
  }

  .ortodontistas-dropdown {
    z-index: 1000001;
  }

  .search-label {
    font-size: 0.85rem;
  }

  .search-input-modern {
    padding: 0.5rem 2rem 0.5rem 0.7rem;
    font-size: 0.85rem;
  }
}

/* Garantir que o painel ocupe 100% da largura */
.main-page-content {
  padding: 0;
}

.main-page-content .row {
  margin: 0;
}

.main-page-content .col-12 {
  padding: 0;
}

/* Ajustar espaçamento da tabela para ficar grudada no painel */
.main-page-content .easy-data-table {
  margin-top: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

/* Ajustar loading e empty states */
.main-page-content .spinner-border {
  margin-top: 2rem;
}

.main-page-content .v-table {
  margin-top: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

/* Garantir que a tabela não interfira no dropdown */
:deep(.easy-data-table) {
  position: relative;
  z-index: 1;
}

:deep(.easy-data-table__header) {
  position: relative;
  z-index: 1;
}

:deep(.easy-data-table__body) {
  position: relative;
  z-index: 1;
}
</style>

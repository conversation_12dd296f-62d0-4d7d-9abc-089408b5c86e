import html2pdf from 'html2pdf.js';
import moment from 'moment';

/**
 * Service for generating PDF documents from HTML content
 * @param {HTMLElement} element - The HTML element to convert to PDF
 * @param {Object} options - Options for PDF generation
 * @param {boolean} openInNewTab - Whether to open the PDF in a new tab (true) or download it (false)
 */
export const generatePDF = async (element, options = {}, openInNewTab = true) => {
  // Default options
  const defaultOptions = {
    margin: [10, 10, 10, 10],
    filename: 'documento.pdf',
    image: { type: 'jpeg', quality: 0.98 },
    html2canvas: {
      scale: 2,
      useCORS: true,
      logging: false
    },
    jsPDF: {
      unit: 'mm',
      format: 'a4',
      orientation: 'portrait'
    }
  };

  // Merge default options with provided options
  const mergedOptions = { ...defaultOptions, ...options };

  try {
    if (openInNewTab) {
      // Generate PDF and open in new tab
      const pdf = await html2pdf()
        .set(mergedOptions)
        .from(element)
        .outputPdf('blob');

      // Create a URL for the PDF blob
      const pdfUrl = URL.createObjectURL(pdf);

      // Open the PDF in a new tab
      window.open(pdfUrl, '_blank');

      return pdfUrl;
    } else {
      // Generate PDF and download
      return await html2pdf()
        .set(mergedOptions)
        .from(element)
        .save();
    }
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  }
};

/**
 * Generate a PDF of the calendar view with consultations
 * @param {HTMLElement} calendarElement - The calendar element to capture
 * @param {string} viewType - The current view type (day, week, month)
 * @param {Date} currentDate - The current date being displayed
 * @param {Array} events - The events/consultations being displayed
 * @param {boolean} openInNewTab - Whether to open the PDF in a new tab (true) or download it (false)
 */
export const generateCalendarPDF = async (calendarElement, viewType, currentDate, events, openInNewTab = true) => {
  if (!calendarElement) {
    console.error('Calendar element not found');
    return;
  }

  // Ensure currentDate is a valid date object
  const dateObj = moment(currentDate).isValid() ? moment(currentDate) : moment();

  // Format the date for the filename
  const formattedDate = dateObj.format('YYYY-MM-DD');
  const formattedMonth = dateObj.format('YYYY-MM');

  // Prepare date variables for filename
  const weekStart = dateObj.clone().startOf('week').format('DD-MM');
  const weekEnd = dateObj.clone().endOf('week').format('DD-MM-YYYY');
  const monthName = dateObj.locale('pt-br').format('MMMM_YYYY');
  const dayFormatted = dateObj.format('DD-MM-YYYY');

  // Create filename based on the view type
  let filename;

  switch (viewType) {
    case 'day':
      filename = `Consultas_Dia_${dayFormatted}.pdf`;
      break;
    case 'week':
      filename = `Consultas_Semana_${weekStart}_a_${weekEnd}.pdf`;
      break;
    case 'month':
      filename = `Consultas_Mes_${monthName}.pdf`;
      break;
    default:
      filename = `Consultas_${dayFormatted}.pdf`;
  }

  // PDF options
  const options = {
    filename: filename,
    margin: [15, 15, 15, 15],
    image: { type: 'jpeg', quality: 0.98 },
    html2canvas: {
      scale: 2,
      useCORS: true,
      logging: false
    },
    jsPDF: {
      unit: 'mm',
      format: 'a4',
      orientation: 'landscape'
    },
    pagebreak: { mode: ['avoid-all', 'css', 'legacy'] }
  };

  try {
    // Create a container for the PDF content
    const container = document.createElement('div');
    container.style.padding = '20px';
    container.style.fontFamily = 'Arial, sans-serif';
    container.className = 'consultations-print-view';

    // Create title based on view type
    let title = '';

    // Ensure currentDate is a valid date object
    const dateObj = moment(currentDate).isValid() ? moment(currentDate) : moment();

    const weekStart = dateObj.clone().startOf('week').format('DD/MM/YYYY');
    const weekEnd = dateObj.clone().endOf('week').format('DD/MM/YYYY');

    switch (viewType) {
      case 'day':
        title = `Consultas do dia ${dateObj.format('DD/MM/YYYY')}`;
        break;
      case 'week':
        title = `Consultas da semana: ${weekStart} - ${weekEnd}`;
        break;
      case 'month':
        title = `Consultas do mês de ${dateObj.locale('pt-br').format('MMMM/YYYY')}`;
        break;
      default:
        title = `Consultas - ${dateObj.format('DD/MM/YYYY')}`;
    }

    // Create HTML structure for the PDF
    container.innerHTML = `
      <style>
        .consultations-print-view {
          font-family: Arial, sans-serif;
          padding: 20px;
          max-width: 100%;
        }
        .print-title {
          text-align: center;
          color: #3f51b5;
          margin-bottom: 20px;
          font-size: 24px;
        }
        .print-date {
          text-align: center;
          margin-bottom: 30px;
          font-size: 16px;
          color: #666;
        }
        .consultations-table {
          width: 100%;
          margin-bottom: 30px;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          border: 1px solid #ddd;
        }
        th, td {
          padding: 10px;
          text-align: left;
          border-bottom: 1px solid #ddd;
        }
        th {
          background-color: #f2f2f2;
          font-weight: bold;
        }
        tr:nth-child(even) {
          background-color: #f9f9f9;
        }
        .status-scheduled {
          color: #3f51b5;
          font-weight: bold;
        }
        .status-confirmed {
          color: #4caf50;
          font-weight: bold;
        }
        .status-completed {
          color: #03a9f4;
          font-weight: bold;
        }
        .status-cancelled {
          color: #f44336;
          font-weight: bold;
        }
        .no-events-message {
          text-align: center;
          padding: 20px;
          font-style: italic;
          color: #666;
        }
        .print-footer {
          margin-top: 30px;
          text-align: center;
          font-size: 12px;
          color: #999;
          border-top: 1px solid #eee;
          padding-top: 10px;
        }
      </style>
      <h1 class="print-title">${title}</h1>
      <div class="consultations-table">
        <table>
          <thead>
            <tr>
              <th>Data</th>
              <th>Horário</th>
              <th>Paciente</th>
              <th>Valor</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            ${events.length > 0 ? events.map(event => {
              // Extract status from keywords
              let statusClass = 'status-scheduled';
              let statusText = 'Agendada';

              if (event.keywords) {
                if (event.keywords.includes('confirmada')) {
                  statusClass = 'status-confirmed';
                  statusText = 'Confirmada';
                } else if (event.keywords.includes('realizada')) {
                  statusClass = 'status-completed';
                  statusText = 'Realizada';
                } else if (event.keywords.includes('cancelada')) {
                  statusClass = 'status-cancelled';
                  statusText = 'Cancelada';
                }
              }

              // Extract value from comment
              let value = '-';
              if (event.comment) {
                const valueMatch = event.comment.match(/R\$\s*([0-9,.]+)/);
                value = valueMatch ? `R$ ${valueMatch[1]}` : '-';
              }

              return `
                <tr>
                  <td>${moment(event.date).format('DD/MM/YYYY')}</td>
                  <td>${moment(event.date).format('HH:mm')}</td>
                  <td>${event.name}</td>
                  <td>${value}</td>
                  <td><span class="${statusClass}">${statusText}</span></td>
                </tr>
              `;
            }).join('') : `
              <tr>
                <td colspan="5" class="no-events-message">
                  Não há consultas agendadas para este período.
                </td>
              </tr>
            `}
          </tbody>
        </table>
      </div>
      <div class="print-footer">
        <p>Gerado em ${moment().format('DD/MM/YYYY HH:mm:ss')}</p>
        <p>Lumi Vision</p>
      </div>
    `;

    // Add to document temporarily (needed for proper rendering)
    document.body.appendChild(container);

    // Generate PDF and open in new tab or download based on parameter
    await generatePDF(container, options, openInNewTab);

    // Remove the temporary element
    document.body.removeChild(container);

    return true;
  } catch (error) {
    console.error('Error generating calendar PDF:', error);
    throw error;
  }
};

<template>
  <aside
    id="sidenav-main"
    class="sidenav navbar navbar-vertical navbar-expand-xs rotate-caret fixed-end bg-transparent" style="background: linear-gradient(180deg, #FFF, #fbfdfe, #fbfdfe) !important;"
  >
    <div class="sidenav-header text-center pl-6">
      <v-icon :class="headerIconClass" :style="headerIconStyle">{{ icon }}</v-icon>
    </div>
    <div style="width: 100%; height: 1px; background: linear-gradient(90deg, #fbfdfe, #c7d7e0, #fbfdfe) !important;">

    </div>
    <!-- Use the configurable sidenav if config is provided, otherwise use the slot -->
    <lumi-sidenav-config v-if="config" :config="config" @action="handleAction"></lumi-sidenav-config>
    <slot v-else></slot>
  </aside>
</template>

<script>
import logo from "@/assets/img/logo-ct.png";
import logoDark from "@/assets/img/logo-ct-dark.png";
import lumiLogo from "@/assets/img/lumi/lumi-vision-logo-170.png";
import { mapState, mapMutations } from "vuex";
import LumiSidenavConfig from "./LumiSidenavConfig.vue";

export default {
  name: "index",
  props: {
    icon: String,
    /**
     * Configuration object for the sidenav
     * If provided, the sidenav will be rendered using LumiSidenavConfig
     * Otherwise, the default slot will be used
     */
    config: {
      type: Object,
      default: null
    }
  },
  components: {
    LumiSidenavConfig
  },
  data() {
    return {
      logo,
      logoDark,
      lumiLogo,
    };
  },
  computed: {
    ...mapState(["isRTL", "sidebarType", "isDarkMode"]),

    /**
     * Classe CSS responsiva para o ícone do header
     */
    headerIconClass() {
      return 'lumi-sidenav-header-icon';
    },

    /**
     * Estilo responsivo para o ícone do header
     */
    headerIconStyle() {
      return {
        color: '#5988A8 !important'
      };
    }
  },
  methods: {
    ...mapMutations(["navbarMinimize"]),

    /**
     * Handle action from the sidenav
     * @param {String} action - Action identifier
     * @param {Object} button - Button configuration
     */
    handleAction(action, button) {
      this.$emit('action', action, button);

      // Auto-collapse sidenav after action unless it's a confirmation action
      // or autoCollapse is explicitly set to false
      if (button.autoCollapse !== false && !this.isConfirmationAction(action)) {
        this.collapseSidenav();
      }
    },

    /**
     * Check if an action is a confirmation action that shouldn't auto-collapse
     * @param {String} action - Action identifier
     * @returns {Boolean}
     */
    isConfirmationAction(action) {
      const confirmationActions = ['logout', 'delete', 'remove', 'confirm'];
      return confirmationActions.some(confirmAction =>
        action.toLowerCase().includes(confirmAction)
      );
    },

    /**
     * Collapse the sidenav
     */
    collapseSidenav() {
      // Check if sidenav is open and pinned
      const sidenavElement = document.querySelector(".g-sidenav-show");
      if (sidenavElement && sidenavElement.classList.contains("g-sidenav-pinned")) {
        this.navbarMinimize();
      }
    }
  }
};
</script>

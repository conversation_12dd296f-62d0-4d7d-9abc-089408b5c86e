<template>
  <div class="modal fade" id="modalOrcamento" tabindex="-1" aria-labelledby="modalOrcamentoLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered custom-modal-width">
      <div class="modal-content">
        <div class="modal-header py-2">
          <div class="d-flex align-items-center">
            <div class="modal-icon me-2">
              <i class="fas fa-calculator text-primary"></i>
            </div>
            <div>
              <h5 class="modal-title mb-0" id="modalOrcamentoLabel">Novo Orçamento</h5>
              <small class="text-muted">Criar orçamento completo</small>
            </div>
          </div>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        
        <div class="modal-body p-0">
          <div class="row g-0 modal-body-responsive">
            <!-- Coluna Esquerda - Configurações do Orçamento -->
            <div class="col-lg-7 col-md-6 col-sm-12 p-3 border-end">
              <div class="orcamento-config">
                <!-- Informações Básicas -->
                <div class="row g-3 mb-4">
                  <div class="col-12">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-info-circle me-2"></i>Informações Básicas
                    </h6>
                  </div>

                  <!-- Paciente -->
                  <div class="col-12">
                    <label class="form-label">Paciente</label>
                    <div class="d-flex gap-2">
                      <select class="form-select"
                              :value="orcamentoForm.paciente_id"
                              @change="onPacienteChange($event.target.value)"
                              :class="{ 'is-invalid': errors.paciente_id }">
                        <option value="">Selecione um paciente</option>
                        <option v-for="paciente in pacientes"
                                :key="paciente?.id || Math.random()"
                                :value="paciente?.id">
                          {{ paciente?.nome || 'Paciente sem nome' }}
                        </option>
                      </select>
                      <button type="button"
                              class="btn btn-outline-primary"
                              @click="abrirBuscaPaciente">
                        <i class="fas fa-search"></i>
                      </button>
                    </div>
                    <div v-if="errors.paciente_id" class="invalid-feedback d-block">
                      {{ errors.paciente_id }}
                    </div>
                  </div>

                  <!-- Título e Dentista -->
                  <div class="col-md-8">
                    <label class="form-label">Título do Orçamento</label>
                    <input type="text"
                           class="form-control"
                           :value="orcamentoForm.titulo"
                           @input="updateOrcamentoForm('titulo', $event.target.value)"
                           placeholder="Ex: Tratamento Ortodôntico"
                           :class="{ 'is-invalid': errors.titulo }">
                    <div v-if="errors.titulo" class="invalid-feedback">
                      {{ errors.titulo }}
                    </div>
                  </div>

                  <div class="col-md-6">
                    <label class="form-label">Dentista Responsável</label>
                    <select class="form-select"
                            :value="orcamentoForm.dentista_id"
                            @change="updateOrcamentoForm('dentista_id', $event.target.value)">
                      <option value="">Selecione o dentista</option>
                      <option v-for="dentista in dentistas"
                              :key="dentista?.id || Math.random()"
                              :value="dentista?.id">
                        {{ dentista?.nome || 'Dentista sem nome' }}
                      </option>
                    </select>
                  </div>

                  <div class="col-md-6">
                    <label class="form-label">Data de Validade</label>
                    <input type="date"
                           class="form-control"
                           :value="orcamentoForm.data_validade"
                           @input="updateOrcamentoForm('data_validade', $event.target.value)"
                           :min="minDate">
                  </div>

                  <!-- Descrição -->
                  <div class="col-12">
                    <label class="form-label">Descrição do Tratamento</label>
                    <textarea class="form-control"
                              rows="2"
                              :value="orcamentoForm.descricao"
                              @input="updateOrcamentoForm('descricao', $event.target.value)"
                              placeholder="Descrição geral do tratamento proposto"></textarea>
                  </div>
                </div>




              </div>
            </div>

            <!-- Coluna Direita - Lista de Itens -->
            <div class="col-lg-5 col-md-6 col-sm-12 p-3 bg-light">
              <div class="itens-list">
                <div class="d-flex justify-content-between align-items-center mb-3">
                  <h6 class="text-primary mb-0">
                    <i class="fas fa-list me-2"></i>Itens do Orçamento
                  </h6>
                  <button type="button"
                          class="btn btn-sm btn-primary"
                          @click="openItemModal()">
                    <i class="fas fa-plus me-1"></i>Novo Item
                  </button>
                </div>

                <!-- Lista de Itens - Cards para telas médias/grandes -->
                <div class="itens-container d-none d-md-block itens-container-responsive">
                  <div v-for="(item, index) in orcamentoForm.itens"
                       :key="index"
                       class="item-card mb-2"
                       :class="{ 'new-item-animation': newItemIndex === index }"
                       @click="openItemModal(item, index)">
                    <div class="card h-100 item-hover"
                         :class="{ 'new-item-glow': newItemIndex === index }">
                      <div class="card-body p-2">
                        <div class="d-flex justify-content-between align-items-start mb-1">
                          <h6 class="card-title mb-1 text-truncate small">
                            {{ item?.nome || `Item ${index + 1}` }}
                          </h6>
                          <div class="item-actions">
                            <button type="button"
                                    class="btn btn-sm btn-outline-primary me-1"
                                    @click.stop="openItemModal(item, index)"
                                    title="Editar item">
                              <i class="fas fa-edit"></i>
                            </button>
                            <button type="button"
                                    class="btn btn-sm btn-outline-danger"
                                    @click.stop="removeOrcamentoItem(index)"
                                    :disabled="orcamentoForm.itens.length <= 1"
                                    title="Remover item">
                              <i class="fas fa-trash"></i>
                            </button>
                          </div>
                        </div>

                        <div class="item-details">
                          <div class="d-flex justify-content-between text-sm mb-1">
                            <span class="small">Qtd: {{ item?.quantidade || 0 }}</span>
                            <span class="small">Unit: {{ formatCurrency(item?.valor_unitario || 0) }}</span>
                          </div>

                          <div class="d-flex justify-content-between">
                            <span class="text-muted small">
                              <i class="fas fa-tooth me-1"></i>
                              {{ (item?.dentes?.length || 0) }} {{ (item?.dentes?.length || 0) === 1 ? 'dente selecionado' : 'dentes selecionados' }}
                            </span>
                            <strong class="text-success small">
                              {{ formatCurrency(calculateItemTotal(item || {})) }}
                            </strong>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Lista de Itens - Tabela para telas pequenas -->
                <div class="d-md-none itens-table-container">
                  <div class="table-responsive">
                    <table class="table table-sm table-hover mb-0">
                      <thead class="table-light">
                        <tr>
                          <th class="small">Item</th>
                          <th class="small text-center">Qtd</th>
                          <th class="small text-end">Total</th>
                          <th class="small text-center">Ações</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="(item, index) in orcamentoForm.itens"
                            :key="index"
                            @click="openItemModal(item, index)"
                            :class="{ 'new-item-row-animation': newItemIndex === index }"
                            style="cursor: pointer;">
                          <td class="small">
                            <div class="fw-bold">{{ item?.nome || `Item ${index + 1}` }}</div>
                            <div class="text-muted" style="font-size: 0.7rem;">
                              {{ (item?.dentes?.length || 0) }} {{ (item?.dentes?.length || 0) === 1 ? 'dente' : 'dentes' }}
                            </div>
                          </td>
                          <td class="text-center small">{{ item?.quantidade || 1 }}</td>
                          <td class="text-end small fw-bold text-success">
                            {{ formatCurrency(calculateItemTotal(item || {})) }}
                          </td>
                          <td class="text-center">
                            <div class="btn-group btn-group-sm">
                              <button type="button"
                                      class="btn btn-outline-primary btn-sm"
                                      @click.stop="openItemModal(item, index)"
                                      title="Editar">
                                <i class="fas fa-edit"></i>
                              </button>
                              <button type="button"
                                      class="btn btn-outline-danger btn-sm"
                                      @click.stop="removeOrcamentoItem(index)"
                                      :disabled="orcamentoForm.itens.length <= 1"
                                      title="Remover">
                                <i class="fas fa-trash"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                <!-- Total Geral -->
                <div class="total-section mt-3 p-3 bg-white rounded border">
                  <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Total Geral:</h5>
                    <h4 class="mb-0 text-success">{{ formatCurrency(totalGeral) }}</h4>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Overlay para Edição de Item -->
        <div v-if="showItemEditor" class="item-editor-overlay">
          <div class="item-editor-panel">
            <div class="item-editor-header">
              <div>
                <h6 class="mb-0">
                  <i class="fas fa-tooth me-2"></i>
                  {{ editingItemIndex !== null ? 'Editar Item' : 'Novo Item' }}
                </h6>
                <small class="text-muted">Configure o procedimento</small>
              </div>
              <button type="button"
                      class="btn btn-sm btn-light"
                      @click="closeItemEditor">
                <i class="fas fa-times"></i>
              </button>
            </div>

            <div class="item-editor-body">
              <div class="row g-3">
                <!-- Serviço e Nome na mesma linha -->
                <div class="col-md-6">
                  <label class="form-label">Serviço/Procedimento</label>
                  <select class="form-select form-select-sm"
                          v-model="itemForm.servico_produto_id"
                          @change="onServicoChange">
                    <option value="">Selecione um serviço</option>
                    <option v-for="servico in servicosProdutos"
                            :key="servico?.id || Math.random()"
                            :value="servico?.id">
                      {{ servico?.nome || 'Serviço sem nome' }}
                    </option>
                  </select>
                </div>

                <div class="col-md-6">
                  <label class="form-label">Nome do Procedimento *</label>
                  <input type="text"
                         class="form-control form-control-sm"
                         v-model="itemForm.nome"
                         placeholder="Nome do procedimento"
                         :class="{ 'is-invalid': itemErrors.nome }">
                  <div v-if="itemErrors.nome" class="invalid-feedback">
                    {{ itemErrors.nome }}
                  </div>
                </div>

                <!-- Quantidade, Valor e Desconto -->
                <div class="col-md-4">
                  <label class="form-label">Quantidade *</label>
                  <input type="number"
                         step="0.01"
                         min="0.01"
                         class="form-control form-control-sm"
                         v-model="itemForm.quantidade"
                         :class="{ 'is-invalid': itemErrors.quantidade }">
                  <div v-if="itemErrors.quantidade" class="invalid-feedback">
                    {{ itemErrors.quantidade }}
                  </div>
                </div>

                <div class="col-md-4">
                  <label class="form-label">Valor Unitário *</label>
                  <div class="input-group input-group-sm">
                    <span class="input-group-text">R$</span>
                    <input type="number"
                           step="0.01"
                           min="0.01"
                           class="form-control"
                           v-model="itemForm.valor_unitario"
                           :class="{ 'is-invalid': itemErrors.valor_unitario }">
                  </div>
                  <div v-if="itemErrors.valor_unitario" class="invalid-feedback">
                    {{ itemErrors.valor_unitario }}
                  </div>
                </div>

                <div class="col-md-4">
                  <label class="form-label">Desconto (%)</label>
                  <input type="number"
                         step="0.01"
                         min="0"
                         max="100"
                         class="form-control form-control-sm"
                         v-model="itemForm.desconto_percentual"
                         placeholder="0">
                </div>

                <!-- Seletor de Dentes -->
                <div class="col-12">
                  <dente-selector v-model="itemForm.dentes" />
                </div>

                <!-- Observações -->
                <div class="col-12">
                  <label class="form-label">Observações</label>
                  <textarea class="form-control form-control-sm"
                            rows="2"
                            v-model="itemForm.observacoes"
                            placeholder="Observações específicas deste item"></textarea>
                </div>
              </div>
            </div>

            <div class="item-editor-footer">
              <button type="button"
                      class="btn btn-light btn-sm"
                      @click="closeItemEditor">
                <i class="fas fa-times me-2"></i>
                Cancelar
              </button>
              <button type="button"
                      class="btn btn-primary btn-sm"
                      @click="saveItem"
                      :disabled="savingItem">
                <i class="fas fa-save me-2"></i>
                {{ savingItem ? 'Salvando...' : 'Salvar Item' }}
              </button>
            </div>
          </div>
        </div>

        <div class="modal-footer py-2">
          <button type="button" class="btn btn-sm btn-light" data-bs-dismiss="modal">
            <i class="fas fa-times me-1"></i>
            Cancelar
          </button>
          <button type="button" class="btn btn-sm btn-info" @click="generatePDF" :disabled="saving || generatingPDF">
            <span v-if="generatingPDF" class="spinner-border spinner-border-sm me-1" role="status"></span>
            <i v-else class="fas fa-file-pdf me-1"></i>
            {{ generatingPDF ? 'Gerando...' : 'PDF' }}
          </button>
          <button type="button" class="btn btn-sm btn-success" @click="saveAndGenerateFatura" :disabled="saving">
            <span v-if="saving" class="spinner-border spinner-border-sm me-1" role="status"></span>
            <i v-else class="fas fa-file-invoice me-1"></i>
            {{ saving ? 'Gerando...' : 'Gerar Fatura' }}
          </button>
          <button type="button" class="btn btn-sm btn-primary" @click="save" :disabled="saving">
            <span v-if="saving" class="spinner-border spinner-border-sm me-1" role="status"></span>
            <i v-else class="fas fa-save me-1"></i>
            {{ saving ? 'Salvando...' : 'Salvar' }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal de Busca de Paciente -->
  <paciente-busca-modal
    ref="pacienteBuscaModal"
    @paciente-selecionado="onPacienteSelecionado"
  />


</template>

<script>
import { orcamentoService } from '@/services/orcamentoService';
import { servicoProdutoService } from '@/services/servicoProdutoService';
import { openModal, closeModal } from '@/utils/modalHelper';
import { searchPacientes } from '@/services/pacientesService';
import { getDentistas } from '@/services/dentistasService';
import { generatePDF } from '@/services/pdfService';
import cSwal from '@/utils/cSwal.js';
import OrcamentoForm from './OrcamentoForm.vue';
import PacienteBuscaModal from '@/components/Global/PacienteBuscaModal.vue';
import DenteSelector from './DenteSelector.vue';


export default {
  name: 'OrcamentoModal',
  emits: ['saved', 'generate-fatura'],
  components: {
    OrcamentoForm,
    PacienteBuscaModal,
    DenteSelector,
  },
  data() {
    return {
      saving: false,
      generatingPDF: false,
      preselectedPaciente: null,
      pacienteSelecionado: null,
      pacientes: [],
      dentistas: [],
      servicosProdutos: [],
      orcamentoForm: {
        paciente_id: '',
        dentista_id: '',
        titulo: '',
        descricao: '',
        data_validade: '',
        observacoes: '',
        itens: [this.createEmptyItem()]
      },
      errors: {},

      // Editor de item interno
      showItemEditor: false,
      editingItemIndex: null,
      savingItem: false,
      newItemIndex: null, // Para animação do item recém-adicionado
      itemForm: {
        servico_produto_id: null,
        nome: '',
        descricao: '',
        quantidade: 1,
        valor_unitario: 0,
        desconto_percentual: 0,
        desconto_valor: 0,
        observacoes: '',
        dentes: []
      },
      itemErrors: {}
    };
  },
  computed: {
    minDate() {
      return new Date().toISOString().split('T')[0];
    },
    totalGeral() {
      if (!this.orcamentoForm?.itens) return 0;
      return this.orcamentoForm.itens.reduce((sum, item) => sum + this.calculateItemTotal(item), 0);
    },

  },
  methods: {
    open(pacienteId = null, pacienteObj = null) {
      this.preselectedPaciente = pacienteId;
      this.pacienteSelecionado = pacienteObj;
      this.resetForm();
      this.clearErrors();

      if (pacienteId) {
        this.orcamentoForm.paciente_id = pacienteId;
      }

      openModal('modalOrcamento');
    },

    resetForm() {
      this.orcamentoForm = {
        paciente_id: this.preselectedPaciente || '',
        dentista_id: '',
        titulo: '',
        descricao: '',
        data_validade: '',
        observacoes: '',
        itens: [this.createEmptyItem()]
      };
    },

    clearErrors() {
      this.errors = {};
    },

    updateOrcamentoForm(field, value) {
      this.orcamentoForm[field] = value;
    },

    openItemModal(item = null, index = null) {
      this.editingItemIndex = index;

      if (item) {
        // Editar item existente
        this.itemForm = { ...item };
      } else {
        // Novo item
        this.resetItemForm();
      }

      this.clearItemErrors();
      this.showItemEditor = true;
    },

    closeItemEditor() {
      this.showItemEditor = false;
      this.editingItemIndex = null;
      this.resetItemForm();
      this.clearItemErrors();
    },

    resetItemForm() {
      this.itemForm = {
        servico_produto_id: null,
        nome: '',
        descricao: '',
        quantidade: 1,
        valor_unitario: 0,
        desconto_percentual: 0,
        desconto_valor: 0,
        observacoes: '',
        dentes: []
      };
    },

    clearItemErrors() {
      this.itemErrors = {};
    },

    onServicoChange() {
      if (!this.itemForm.servico_produto_id) return;

      const servico = this.servicosProdutos.find(s => s.id == this.itemForm.servico_produto_id);
      if (servico) {
        this.itemForm.nome = servico.nome || '';
        this.itemForm.valor_unitario = servico.valor_base || 0;
        this.itemForm.descricao = servico.descricao || '';
      }
    },

    validateItemForm() {
      this.itemErrors = {};

      if (!this.itemForm.nome || this.itemForm.nome.trim() === '') {
        this.itemErrors.nome = 'Nome do procedimento é obrigatório';
      }

      if (!this.itemForm.quantidade || this.itemForm.quantidade <= 0) {
        this.itemErrors.quantidade = 'Quantidade deve ser maior que zero';
      }

      if (!this.itemForm.valor_unitario || this.itemForm.valor_unitario <= 0) {
        this.itemErrors.valor_unitario = 'Valor unitário deve ser maior que zero';
      }

      return Object.keys(this.itemErrors).length === 0;
    },

    saveItem() {
      if (!this.validateItemForm()) {
        return;
      }

      this.savingItem = true;

      if (this.editingItemIndex !== null) {
        // Editar item existente
        const updatedItens = [...this.orcamentoForm.itens];
        updatedItens[this.editingItemIndex] = { ...this.itemForm };
        this.orcamentoForm.itens = updatedItens;
      } else {
        // Adicionar novo item
        this.orcamentoForm.itens.push({ ...this.itemForm });
        // Definir índice do novo item para animação
        this.newItemIndex = this.orcamentoForm.itens.length - 1;

        // Remover animação após 3 segundos
        setTimeout(() => {
          this.newItemIndex = null;
        }, 3000);
      }

      setTimeout(() => {
        this.savingItem = false;
        this.closeItemEditor();
      }, 300);
    },

    onPacienteChange(pacienteId) {
      this.orcamentoForm.paciente_id = pacienteId;
      const paciente = this.pacientes.find(p => p.id == pacienteId);
      this.pacienteSelecionado = paciente || null;
    },

    calculateItemTotal(item) {
      if (!item) return 0;
      const valorBruto = (item.quantidade || 0) * (item.valor_unitario || 0);
      const desconto = (item.desconto_percentual || 0) > 0
        ? valorBruto * (item.desconto_percentual / 100)
        : (item.desconto_valor || 0);
      return Math.max(0, valorBruto - desconto);
    },

    formatCurrency(value) {
      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(value || 0);
    },



    createEmptyItem() {
      return {
        servico_produto_id: null,
        nome: '',
        descricao: '',
        quantidade: 1,
        valor_unitario: 0,
        desconto_percentual: 0,
        desconto_valor: 0,
        observacoes: '',
        dentes: []
      };
    },

    removeOrcamentoItem(index) {
      if (this.orcamentoForm.itens.length > 1) {
        this.orcamentoForm.itens.splice(index, 1);
      }
    },

    async searchServicos(termo) {
      try {
        const response = await servicoProdutoService.buscarParaOrcamento({ busca: termo });
        this.servicosProdutos = response.data.data || [];
      } catch (error) {
        console.error('Erro ao buscar serviços:', error);
      }
    },

    async save() {
      if (!this.validateForm()) {
        return;
      }

      this.saving = true;
      try {
        await this.saveOrcamento();
        this.closeModal();
        this.$emit('saved');
      } catch (error) {
        console.error('Erro ao salvar:', error);
        
        if (error.response && error.response.status === 422) {
          this.errors = error.response.data.data || {};
        } else {
          cSwal.cError('Erro ao salvar orçamento');
        }
      } finally {
        this.saving = false;
      }
    },

    async saveAndGenerateFatura() {
      if (!this.validateForm()) {
        return;
      }

      this.saving = true;
      try {
        const orcamento = await this.saveOrcamento();
        this.closeModal();
        
        // Emitir evento para abrir modal de fatura com dados do orçamento
        this.$emit('generate-fatura', orcamento);
      } catch (error) {
        console.error('Erro ao salvar:', error);
        
        if (error.response && error.response.status === 422) {
          this.errors = error.response.data.data || {};
        } else {
          cSwal.cError('Erro ao salvar orçamento');
        }
      } finally {
        this.saving = false;
      }
    },

    async saveOrcamento() {
      const response = await orcamentoService.createOrcamento(this.orcamentoForm);
      cSwal.cSuccess('Orçamento criado com sucesso');
      return response.data;
    },

    validateForm() {
      console.log('Validando formulário:', this.orcamentoForm);
      const validation = orcamentoService.validateOrcamentoData(this.orcamentoForm);
      console.log('Resultado da validação:', validation);
      this.errors = validation.errors;
      return validation.isValid;
    },

    closeModal() {
      closeModal('modalOrcamento');
    },

    async generatePDF() {
      if (!this.validateForm()) {
        return;
      }

      this.generatingPDF = true;
      try {
        // Criar um elemento HTML temporário com o orçamento
        const orcamentoElement = this.createOrcamentoHTML();

        // Adicionar ao DOM temporariamente
        document.body.appendChild(orcamentoElement);

        // Gerar PDF
        const filename = `Orcamento_${this.pacienteSelecionado?.nome || 'Paciente'}_${new Date().toISOString().split('T')[0]}.pdf`;
        await generatePDF(orcamentoElement, { filename }, true);

        // Remover elemento temporário
        document.body.removeChild(orcamentoElement);

        cSwal.cSuccess('PDF do orçamento gerado com sucesso!');
      } catch (error) {
        console.error('Erro ao gerar PDF:', error);
        cSwal.cError('Erro ao gerar PDF do orçamento');
      } finally {
        this.generatingPDF = false;
      }
    },

    createOrcamentoHTML() {
      const valorTotal = this.orcamentoForm.itens.reduce((sum, item) => {
        return sum + (item.quantidade * item.valor_unitario);
      }, 0);

      const container = document.createElement('div');
      container.style.padding = '20px';
      container.style.fontFamily = 'Arial, sans-serif';
      container.className = 'orcamento-print-view';

      container.innerHTML = `
        <style>
          .orcamento-print-view {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 100%;
          }
          .print-title {
            text-align: center;
            color: #3f51b5;
            margin-bottom: 20px;
            font-size: 24px;
          }
          .print-info {
            margin-bottom: 30px;
          }
          .print-info table {
            width: 100%;
            border-collapse: collapse;
          }
          .print-info td {
            padding: 8px;
            border-bottom: 1px solid #eee;
          }
          .print-info td:first-child {
            font-weight: bold;
            width: 150px;
          }
          .itens-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
          }
          .itens-table th, .itens-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
          }
          .itens-table th {
            background-color: #f2f2f2;
            font-weight: bold;
          }
          .total-section {
            text-align: right;
            font-size: 18px;
            font-weight: bold;
            margin-top: 20px;
          }
          .print-footer {
            margin-top: 40px;
            text-align: center;
            color: #666;
            font-size: 12px;
          }
        </style>
        <h1 class="print-title">${this.orcamentoForm.titulo || 'Orçamento'}</h1>

        <div class="print-info">
          <table>
            <tr>
              <td>Paciente:</td>
              <td>${this.pacienteSelecionado?.nome || 'Não informado'}</td>
            </tr>
            <tr>
              <td>Data:</td>
              <td>${new Date().toLocaleDateString('pt-BR')}</td>
            </tr>
            <tr>
              <td>Validade:</td>
              <td>${this.orcamentoForm.data_validade ? new Date(this.orcamentoForm.data_validade).toLocaleDateString('pt-BR') : 'Não informada'}</td>
            </tr>
            ${this.orcamentoForm.descricao ? `
            <tr>
              <td>Descrição:</td>
              <td>${this.orcamentoForm.descricao}</td>
            </tr>
            ` : ''}
          </table>
        </div>

        <table class="itens-table">
          <thead>
            <tr>
              <th>Item</th>
              <th>Dentes</th>
              <th>Quantidade</th>
              <th>Valor Unitário</th>
              <th>Total</th>
            </tr>
          </thead>
          <tbody>
            ${this.orcamentoForm.itens.map(item => `
              <tr>
                <td>
                  <strong>${item.nome || 'Item sem nome'}</strong>
                  ${item.descricao ? `<br><small style="color: #666;">${item.descricao}</small>` : ''}
                </td>
                <td>${item.dentes && item.dentes.length > 0 ? item.dentes.sort((a, b) => a - b).join(', ') : '-'}</td>
                <td>${item.quantidade}</td>
                <td>R$ ${(item.valor_unitario || 0).toFixed(2).replace('.', ',')}</td>
                <td>R$ ${((item.quantidade || 0) * (item.valor_unitario || 0)).toFixed(2).replace('.', ',')}</td>
              </tr>
              ${item.observacoes ? `
              <tr>
                <td colspan="5" style="padding: 5px 10px; font-size: 12px; color: #666; border-bottom: none;">
                  <em>Obs: ${item.observacoes}</em>
                </td>
              </tr>
              ` : ''}
            `).join('')}
          </tbody>
        </table>

        <div class="total-section">
          Total Geral: R$ ${valorTotal.toFixed(2).replace('.', ',')}
        </div>

        ${this.orcamentoForm.observacoes ? `
        <div style="margin-top: 30px;">
          <strong>Observações:</strong><br>
          ${this.orcamentoForm.observacoes}
        </div>
        ` : ''}

        <div class="print-footer">
          <p>Gerado em ${new Date().toLocaleDateString('pt-BR')} às ${new Date().toLocaleTimeString('pt-BR')}</p>
          <p>Lumi Vision</p>
        </div>
      `;

      return container;
    },

    abrirBuscaPaciente() {
      this.$refs.pacienteBuscaModal.open();
    },

    onPacienteSelecionado(paciente) {
      this.pacienteSelecionado = paciente;
      this.orcamentoForm.paciente_id = paciente.id;
    },

    limparPaciente() {
      this.pacienteSelecionado = null;
      this.orcamentoForm.paciente_id = '';
    },

    async loadPacientes() {
      try {
        const response = await searchPacientes();
        this.pacientes = response || [];
      } catch (error) {
        console.error('Erro ao carregar pacientes:', error);
        this.pacientes = [];
      }
    },

    async loadDentistas() {
      try {
        const response = await getDentistas();
        this.dentistas = response || [];
      } catch (error) {
        console.error('Erro ao carregar dentistas:', error);
        this.dentistas = [];
      }
    },

    async loadServicosProdutos() {
      try {
        const response = await servicoProdutoService.getServicosProdutos({ ativo: 1 });
        this.servicosProdutos = response.data.data || [];
        console.log('Serviços carregados:', this.servicosProdutos.length);
      } catch (error) {
        console.error('Erro ao carregar serviços/produtos:', error);
        this.servicosProdutos = [];
      }
    }
  },

  mounted() {
    this.loadPacientes();
    this.loadDentistas();
    this.loadServicosProdutos();
  }
};
</script>

<style scoped>
/* Modal customizado */
@media (min-width: 1200px) {
  .custom-modal-width {
    max-width: 1400px;
  }
}

/* Modal grande para telas pequenas */
@media (max-width: 1199px) {
  .custom-modal-width {
    width: 95vw !important;
    height: 95vh !important;
    max-width: 1100px !important;
    max-height: 900px !important;
    margin: 2.5vh auto !important;
  }

  .custom-modal-width .modal-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .custom-modal-width .modal-body {
    flex: 1;
    overflow: hidden;
  }
}

/* Responsividade para telas pequenas */
.modal-body-responsive {
  min-height: 500px;
}

@media (max-height: 700px) {
  .modal-body-responsive {
    min-height: auto;
  }
}

@media (max-width: 767px) {
  .modal-body-responsive {
    min-height: auto;
    flex-direction: column;
  }

  .modal-body-responsive .col-sm-12 {
    max-width: 100%;
    flex: 0 0 auto;
  }
}

/* Container de itens responsivo */
.itens-container-responsive {
  max-height: 400px;
  overflow-y: auto;
}

@media (max-height: 800px) {
  .itens-container-responsive {
    max-height: 350px;
  }
}

@media (max-height: 700px) {
  .itens-container-responsive {
    max-height: 280px;
  }
}

@media (max-height: 600px) {
  .itens-container-responsive {
    max-height: 220px;
  }
}

.itens-table-container {
  max-height: 350px;
  overflow-y: auto;
}

@media (max-height: 800px) {
  .itens-table-container {
    max-height: 300px;
  }
}

@media (max-height: 700px) {
  .itens-table-container {
    max-height: 250px;
  }
}

@media (max-height: 600px) {
  .itens-table-container {
    max-height: 200px;
  }
}

/* Reutilizar estilos do FinanceiroCreateModal */
.elegant-modal {
  border-radius: 20px;
  border: none;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.elegant-header {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
}

.modal-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  font-size: 1.2rem;
}

.elegant-close {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  opacity: 1;
  color: white;
  font-size: 1.2rem;
  padding: 0.5rem;
  transition: all 0.3s ease;
}

.elegant-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.elegant-body {
  padding: 2rem;
  background: #f8f9fa;
}

.elegant-footer {
  background: white;
  border: none;
  padding: 0.75rem 1.5rem;
  gap: 1rem;
}

.elegant-btn-primary {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border: none;
  border-radius: 10px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.elegant-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

/* Estilos para a nova interface de duas colunas */
.item-card {
  cursor: pointer;
  transition: all 0.2s ease;
}

.item-card .item-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.item-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.item-card:hover .item-actions {
  opacity: 1;
}

.modal-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white !important;
  border-bottom: none;
}

.modal-header * {
  color: white !important;
}

.modal-header .btn-close {
  filter: invert(1);
}

.modal-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

/* Animações para itens novos */
.new-item-animation {
  animation: newItemPulse 3s ease-in-out;
}

.new-item-glow {
  animation: newItemGlow 3s ease-in-out;
}

.new-item-row-animation {
  animation: newItemRowHighlight 3s ease-in-out;
}

@keyframes newItemPulse {
  0%, 100% { transform: scale(1); }
  25%, 75% { transform: scale(1.02); }
  50% { transform: scale(1.05); }
}

@keyframes newItemGlow {
  0%, 100% {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-color: #dee2e6;
  }
  25%, 75% {
    box-shadow: 0 4px 20px rgba(40, 167, 69, 0.3);
    border-color: #28a745;
  }
  50% {
    box-shadow: 0 6px 30px rgba(40, 167, 69, 0.5);
    border-color: #28a745;
  }
}

@keyframes newItemRowHighlight {
  0%, 100% {
    background-color: transparent;
  }
  25%, 75% {
    background-color: rgba(40, 167, 69, 0.1);
  }
  50% {
    background-color: rgba(40, 167, 69, 0.2);
  }
}

/* Overlay para Editor de Item */
.item-editor-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(3px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
  animation: fadeIn 0.3s ease;
}

.item-editor-panel {
  background: white;
  border-radius: 15px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 95%;
  max-width: 750px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

@media (max-width: 1199px) {
  .item-editor-panel {
    width: 90%;
    max-width: 700px;
    max-height: 85vh;
  }
}

@media (max-width: 768px) {
  .item-editor-panel {
    width: 98%;
    max-height: 95vh;
    border-radius: 10px;
  }
}

.item-editor-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  padding: 0.75rem 1.25rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: none;
}

.item-editor-header h6 {
  color: white !important;
  margin: 0;
}

.item-editor-header small {
  color: rgba(255, 255, 255, 0.8) !important;
}

.item-editor-body {
  padding: 1.25rem;
  max-height: 70vh;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .item-editor-body {
    padding: 1rem;
    max-height: 75vh;
  }
}

.item-editor-footer {
  padding: 0.75rem 1.25rem;
  border-top: 1px solid #dee2e6;
  background: #f8f9fa;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.itens-container::-webkit-scrollbar {
  width: 6px;
}

.itens-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.itens-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.itens-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.text-sm {
  font-size: 0.875rem;
}

.border-end {
  border-right: 1px solid #dee2e6 !important;
}
</style>

<template>
  <div class="paciente-financeiro">
    <!-- Container Principal com Duas Colunas -->
    <div class="card rounded-0 mb-3">
      <div class="card-body p-0">
        <div class="row g-0 h-100">

          <!-- ========== COLUNA ESQUERDA - ORÇAMENTOS ========== -->
          <div class="col-lg-4 col-md-5 border-end">
            <div class="orcamentos-section h-100">
              <!-- Header dos Orçamentos -->
              <div class="section-header bg-gradient-blue-subtle text-dark">
                <div class="d-flex justify-content-between align-items-center">
                  <div class="d-flex align-items-center">
                    <i class="fas fa-calculator me-2 text-primary"></i>
                    <h6 class="mb-0 font-weight-bold">Orçamentos</h6>
                    <span v-if="orcamentos.length > 0" class="badge bg-primary ms-2">{{ orcamentos.length }}</span>
                  </div>
                  <button class="btn btn-sm btn-primary" @click="$emit('create-orcamento')">
                    <i class="fas fa-plus me-1"></i>
                    Novo
                  </button>
                </div>
              </div>

              <!-- Conteúdo dos Orçamentos -->
              <div class="section-content">
                <div v-if="loading.orcamentos" class="text-center py-4">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Carregando orçamentos...</span>
                  </div>
                </div>
                <div v-else-if="orcamentos.length === 0" class="text-center py-4 text-muted">
                  <i class="fas fa-calculator fa-2x mb-2 opacity-50"></i>
                  <p class="mb-0 small">Nenhum orçamento encontrado</p>
                  <small class="text-muted">Clique em "Novo" para criar</small>
                </div>
                <div v-else class="orcamentos-list">
                  <div
                    v-for="orcamento in orcamentos"
                    :key="orcamento.id"
                    class="orcamento-item"
                    @click="$emit('view-orcamento', orcamento)"
                  >
                    <div class="orcamento-item-header">
                      <div class="orcamento-item-title">
                        <h6 class="mb-0 small">{{ orcamento.titulo }}</h6>
                        <small class="text-muted">#{{ orcamento.numero || orcamento.id }}</small>
                      </div>
                      <span class="badge badge-sm" :class="getOrcamentoStatusBadgeClass(orcamento.status)">
                        {{ getOrcamentoStatusText(orcamento.status) }}
                      </span>
                    </div>

                    <div class="orcamento-item-body">
                      <div class="orcamento-item-info">
                        <div class="info-row">
                          <i class="fas fa-calendar-alt text-muted me-1"></i>
                          <small>{{ formatDate(orcamento.data_orcamento) }}</small>
                        </div>
                        <div class="info-row" v-if="orcamento.data_validade">
                          <i class="fas fa-clock text-muted me-1"></i>
                          <small>Válido até {{ formatDate(orcamento.data_validade) }}</small>
                        </div>
                      </div>

                      <div class="orcamento-item-valor">
                        <div class="valor-principal">{{ formatCurrency(orcamento.valor_final) }}</div>
                        <small v-if="orcamento.valor_total !== orcamento.valor_final" class="valor-original text-muted">
                          De {{ formatCurrency(orcamento.valor_total) }}
                        </small>
                      </div>
                    </div>

                    <div class="orcamento-item-actions">
                      <button
                        class="btn btn-sm btn-outline-primary"
                        @click.stop="$emit('edit-orcamento', orcamento)"
                        title="Editar"
                      >
                        <i class="fas fa-edit"></i>
                      </button>
                      <button
                        v-if="orcamento.status === 'aprovado'"
                        class="btn btn-sm btn-success"
                        @click.stop="$emit('convert-orcamento', orcamento)"
                        title="Converter em Fatura"
                      >
                        <i class="fas fa-arrow-right"></i>
                      </button>
                      <div class="dropdown">
                        <button
                          class="btn btn-sm btn-outline-secondary"
                          data-bs-toggle="dropdown"
                          @click.stop
                          title="Mais opções"
                        >
                          <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                          <li>
                            <a class="dropdown-item" href="#" @click.prevent.stop="$emit('duplicate-orcamento', orcamento)">
                              <i class="fas fa-copy me-2"></i>
                              Duplicar
                            </a>
                          </li>
                          <li>
                            <a class="dropdown-item" href="#" @click.prevent.stop="$emit('send-orcamento', orcamento)">
                              <i class="fas fa-paper-plane me-2"></i>
                              Enviar
                            </a>
                          </li>
                          <li>
                            <a class="dropdown-item" href="#" @click.prevent.stop="$emit('download-orcamento', orcamento)">
                              <i class="fas fa-download me-2"></i>
                              Download PDF
                            </a>
                          </li>
                          <li><hr class="dropdown-divider"></li>
                          <li>
                            <a class="dropdown-item text-danger" href="#" @click.prevent.stop="$emit('delete-orcamento', orcamento.id)">
                              <i class="fas fa-trash me-2"></i>
                              Excluir
                            </a>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- ========== COLUNA DIREITA - FATURAS ========== -->
          <div class="col-lg-8 col-md-7">
            <div class="faturas-section h-100">
              <!-- Header das Faturas -->
              <div class="section-header bg-light">
                <div class="d-flex justify-content-between align-items-center">
                  <div class="d-flex align-items-center">
                    <i class="fas fa-file-invoice-dollar me-2 text-primary"></i>
                    <h6 class="mb-0 font-weight-bold text-dark">Faturas</h6>
                    <span v-if="faturas.length > 0" class="badge bg-primary ms-2">{{ faturas.length }}</span>
                  </div>
                  <button class="btn btn-sm btn-primary" @click="$emit('create')">
                    <i class="fas fa-plus me-1"></i>
                    Nova Fatura
                  </button>
                </div>
              </div>

              <!-- Filtros das Faturas -->
              <div class="filters-section">
                <div class="row g-2">
                  <div class="col-12">
                    <div class="search-input-wrapper">
                      <i class="fas fa-search search-input-icon"></i>
                      <input type="text" class="form-control form-control-sm search-input-with-icon" v-model="filters.descricao" placeholder="Buscar por descrição...">
                    </div>
                  </div>
                </div>
              </div>


              <!-- Filtros por Status (Cards Clicáveis) -->
              <div class="stats-section">
                <div class="row g-2">
                  <div class="col-6 col-lg-3">
                    <div
                      class="stats-card-toggle stats-card-pago"
                      :class="{ 'active': isStatusActive('pago'), 'disabled': !isStatusActive('pago') }"
                      @click="toggleStatus('pago')"
                    >
                      <div class="stats-icon" :class="isStatusActive('pago') ? 'text-success' : 'text-muted'">
                        <i class="fas fa-check-circle"></i>
                      </div>
                      <div class="stats-content">
                        <div class="stats-title">Pago</div>
                        <div class="stats-value" :class="isStatusActive('pago') ? 'text-success' : 'text-muted'">{{ formatCurrency(totalPago) }}</div>
                      </div>
                      <div class="toggle-checkbox">
                        <i class="fas fa-check" v-if="isStatusActive('pago')"></i>
                      </div>
                    </div>
                  </div>
                  <div class="col-6 col-lg-3">
                    <div
                      class="stats-card-toggle stats-card-pendente"
                      :class="{ 'active': isStatusActive('pendente'), 'disabled': !isStatusActive('pendente') }"
                      @click="toggleStatus('pendente')"
                    >
                      <div class="stats-icon" :class="isStatusActive('pendente') ? 'text-warning' : 'text-muted'">
                        <i class="fas fa-clock"></i>
                      </div>
                      <div class="stats-content">
                        <div class="stats-title">Pendente</div>
                        <div class="stats-value" :class="isStatusActive('pendente') ? 'text-warning' : 'text-muted'">{{ formatCurrency(totalPendente) }}</div>
                      </div>
                      <div class="toggle-checkbox">
                        <i class="fas fa-check" v-if="isStatusActive('pendente')"></i>
                      </div>
                    </div>
                  </div>
                  <div class="col-6 col-lg-3">
                    <div
                      class="stats-card-toggle stats-card-vencido"
                      :class="{ 'active': isStatusActive('vencido'), 'disabled': !isStatusActive('vencido') }"
                      @click="toggleStatus('vencido')"
                    >
                      <div class="stats-icon" :class="isStatusActive('vencido') ? 'text-danger' : 'text-muted'">
                        <i class="fas fa-exclamation-triangle"></i>
                      </div>
                      <div class="stats-content">
                        <div class="stats-title">Vencido</div>
                        <div class="stats-value" :class="isStatusActive('vencido') ? 'text-danger' : 'text-muted'">{{ formatCurrency(totalVencido) }}</div>
                      </div>
                      <div class="toggle-checkbox">
                        <i class="fas fa-check" v-if="isStatusActive('vencido')"></i>
                      </div>
                    </div>
                  </div>
                  <div class="col-6 col-lg-3">
                    <div
                      class="stats-card-toggle stats-card-cancelado"
                      :class="{ 'active': isStatusActive('cancelado'), 'disabled': !isStatusActive('cancelado') }"
                      @click="toggleStatus('cancelado')"
                    >
                      <div class="stats-icon" :class="isStatusActive('cancelado') ? 'text-secondary' : 'text-muted'">
                        <i class="fas fa-times-circle"></i>
                      </div>
                      <div class="stats-content">
                        <div class="stats-title">Cancelado</div>
                        <div class="stats-value" :class="isStatusActive('cancelado') ? 'text-secondary' : 'text-muted'">{{ formatCurrency(totalCancelado) }}</div>
                      </div>
                      <div class="toggle-checkbox">
                        <i class="fas fa-check" v-if="isStatusActive('cancelado')"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>


              <!-- Lista de Faturas -->
              <div class="faturas-table-section">
                <div class="table-responsive">
                  <table class="table align-items-center mb-0">
                <thead>
                  <tr>
                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Descrição
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Valor
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Emissão
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Vencimento
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Status
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-if="loading.faturas">
                    <td colspan="6" class="text-center py-4">
                      <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                      </div>
                    </td>
                  </tr>
                  <tr v-else-if="filteredFaturas.length === 0">
                    <td colspan="6" class="text-center py-4 text-muted">
                      {{ faturas.length === 0 ? 'Nenhuma fatura encontrada para este paciente' : 'Nenhuma fatura corresponde aos filtros aplicados' }}
                    </td>
                  </tr>
                  <tr v-else v-for="fatura in filteredFaturas" :key="fatura.id">
                    <td>
                      <div class="d-flex px-2 py-1">
                        <div class="d-flex flex-column justify-content-center">
                          <h6 class="mb-0 text-sm">{{ fatura.descricao }}</h6>
                          <p class="text-xs text-secondary mb-0" v-if="fatura.observacoes">
                            {{ fatura.observacoes }}
                          </p>
                          <div v-if="fatura.parcelas_total > 1" class="text-xs text-info">
                            Parcela {{ fatura.parcela_numero }}/{{ fatura.parcelas_total }}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="font-weight-bold">{{ formatCurrency(fatura.valor_final) }}</span>
                      <div v-if="fatura.valor_nominal !== fatura.valor_final" class="text-xs text-secondary">
                        Original: {{ formatCurrency(fatura.valor_nominal) }}
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">
                        {{ formatDate(fatura.data_emissao) }}
                      </span>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">
                        {{ formatDate(fatura.data_vencimento) }}
                      </span>
                      <div v-if="isOverdue(fatura)" class="text-xs text-danger">
                        Vencida há {{ getDaysOverdue(fatura) }} dias
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="badge badge-sm" :class="getStatusBadgeClass(fatura.status)">
                        {{ getStatusText(fatura.status) }}
                      </span>
                      <div v-if="fatura.data_pagamento" class="text-xs text-secondary mt-1">
                        Pago em {{ formatDate(fatura.data_pagamento) }}
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <div class="dropdown">
                        <button class="btn btn-link text-secondary mb-0" data-bs-toggle="dropdown">
                          <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                          <li>
                            <a class="dropdown-item" href="#" @click.prevent="$emit('edit', fatura)">
                              <i class="fas fa-edit me-2"></i>
                              Editar
                            </a>
                          </li>
                          <li v-if="fatura.status === 'pendente'">
                            <a class="dropdown-item" href="#" @click.prevent="markAsPaid(fatura)">
                              <i class="fas fa-check me-2"></i>
                              Marcar como Pago
                            </a>
                          </li>
                          <li>
                            <a class="dropdown-item" href="#" @click.prevent="$emit('generate-receipt', fatura)">
                              <i class="fas fa-file-pdf me-2"></i>
                              Gerar Recibo
                            </a>
                          </li>
                          <li>
                            <hr class="dropdown-divider">
                          </li>
                          <li>
                            <a class="dropdown-item text-danger" href="#" @click.prevent="$emit('delete', fatura.id)">
                              <i class="fas fa-trash me-2"></i>
                              Cancelar
                            </a>
                          </li>
                        </ul>
                      </div>
                    </td>
                  </tr>
                </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { financeiroService } from '@/services/financeiroService';

export default {
  name: 'PacienteFinanceiro',
  props: {
    paciente: {
      type: Object,
      required: true
    },
    faturas: {
      type: Array,
      default: () => []
    },
    orcamentos: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Object,
      default: () => ({
        faturas: false,
        orcamentos: false
      })
    }
  },
  data() {
    return {
      filters: {
        statusList: ['pago', 'pendente', 'vencido', 'cancelado'], // Todos ativos por padrão
        descricao: ''
      }
    };
  },
  computed: {
    filteredFaturas() {
      let filtered = [...this.faturas];

      // Filtrar por status (se algum status estiver selecionado)
      if (this.filters.statusList.length > 0) {
        filtered = filtered.filter(fatura => this.filters.statusList.includes(fatura.status));
      }

      // Filtrar por descrição
      if (this.filters.descricao) {
        filtered = filtered.filter(fatura =>
          fatura.descricao.toLowerCase().includes(this.filters.descricao.toLowerCase())
        );
      }

      return filtered.sort((a, b) => new Date(b.data_vencimento) - new Date(a.data_vencimento));
    },

    totalPago() {
      return this.faturas
        .filter(f => f.status === 'pago')
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);
    },

    totalPendente() {
      return this.faturas
        .filter(f => f.status === 'pendente')
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);
    },

    totalVencido() {
      return this.faturas
        .filter(f => f.status === 'vencido' || this.isOverdue(f))
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);
    },

    totalCancelado() {
      return this.faturas
        .filter(f => f.status === 'cancelado')
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);
    },

    totalGeral() {
      return this.faturas
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);
    }
  },
  methods: {
    formatCurrency: financeiroService.formatCurrency,
    formatDate: financeiroService.formatDate,
    getStatusBadgeClass: financeiroService.getStatusBadgeClass,
    getStatusText: financeiroService.getStatusText,

    // Métodos para Orçamentos
    getOrcamentoStatusText(status) {
      const statusMap = {
        'rascunho': 'Rascunho',
        'enviado': 'Enviado',
        'aprovado': 'Aprovado',
        'rejeitado': 'Rejeitado',
        'expirado': 'Expirado',
        'convertido': 'Convertido'
      };
      return statusMap[status] || status;
    },

    getOrcamentoStatusBadgeClass(status) {
      const classMap = {
        'rascunho': 'bg-secondary',
        'enviado': 'bg-info',
        'aprovado': 'bg-success',
        'rejeitado': 'bg-danger',
        'expirado': 'bg-warning',
        'convertido': 'bg-primary'
      };
      return classMap[status] || 'bg-secondary';
    },

    isStatusActive(status) {
      return this.filters.statusList.includes(status);
    },

    toggleStatus(status) {
      const index = this.filters.statusList.indexOf(status);
      if (index > -1) {
        // Remove o status se já estiver selecionado
        this.filters.statusList.splice(index, 1);
      } else {
        // Adiciona o status se não estiver selecionado
        this.filters.statusList.push(status);
      }
    },

    markAsPaid(fatura) {
      const paymentData = {
        data_pagamento: new Date().toISOString().split('T')[0],
        meio_pagamento: 'Não especificado'
      };
      this.$emit('mark-paid', fatura.id, paymentData);
    },

    isOverdue(fatura) {
      if (fatura.status !== 'pendente') return false;
      return new Date(fatura.data_vencimento) < new Date();
    },

    getDaysOverdue(fatura) {
      if (!this.isOverdue(fatura)) return 0;
      const today = new Date();
      const vencimento = new Date(fatura.data_vencimento);
      const diffTime = today - vencimento;
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
  }
};
</script>

<style scoped>
.paciente-financeiro {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.paciente-financeiro .card {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.paciente-financeiro .card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* ========== LAYOUT DE DUAS COLUNAS ========== */
.row.g-0.h-100 {
  min-height: 600px;
}

.orcamentos-section,
.faturas-section {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.section-header {
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.section-content {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
}

.filters-section {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
  flex-shrink: 0;
}

.stats-section {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e9ecef;
  background: #fff;
  flex-shrink: 0;
}

.faturas-table-section {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.faturas-table-section .table-responsive {
  height: 100%;
}

/* Estilos para os cards de estatísticas */
.stats-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 0.8rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.stats-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: #344767;
  text-transform: uppercase;
  margin-bottom: 0.4rem;
  letter-spacing: 0.5px;
}

.stats-value {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.badge-warning {
  background-color: #fb6340;
}

.badge-success {
  background-color: #2dce89;
}

.badge-danger {
  background-color: #f5365c;
}

.badge-secondary {
  background-color: #8898aa;
}

.text-info {
  color: #11cdef !important;
}

/* Botões de Ação Elegantes */
.elegant-action-btn {
  min-width: 140px;
  min-height: 80px;
  border-radius: 12px;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.elegant-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.elegant-action-btn i {
  transition: all 0.3s ease;
}

.elegant-action-btn:hover i {
  transform: scale(1.1);
}

/* Botões de Ação Pequenos */
.elegant-action-btn-small {
  min-width: 120px;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.elegant-action-btn-small:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Estilos para os toggles de status */
.status-toggles {
  display: flex;
  gap: 0;
  border-radius: 0.375rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.status-toggle {
  border-radius: 0 !important;
  border-right: none !important;
  font-size: 0.75rem;
  padding: 0.4rem 0.5rem;
  font-weight: 600;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex: 1;
  text-align: center;
}

.status-toggle:last-child {
  border-right: 1px solid !important;
}

.status-toggle:first-child {
  border-top-left-radius: 0.375rem !important;
  border-bottom-left-radius: 0.375rem !important;
}

.status-toggle:last-child {
  border-top-right-radius: 0.375rem !important;
  border-bottom-right-radius: 0.375rem !important;
}

.status-toggle:hover {
  transform: translateY(-1px);
  z-index: 2;
  position: relative;
}

/* Botões de Ação Verticais */
.elegant-action-btn-vertical {
  min-height: 80px;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 0.75rem 0.5rem;
}

.elegant-action-btn-vertical:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.elegant-action-btn-vertical i {
  transition: all 0.3s ease;
}

.elegant-action-btn-vertical:hover i {
  transform: scale(1.1);
}

.elegant-action-btn-vertical div {
  font-size: 0.85rem;
  line-height: 1.2;
}

/* ========== ESTILOS PARA ORÇAMENTOS EM LISTA ========== */
.orcamentos-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.5rem;
}

.orcamento-item {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 0.75rem;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.orcamento-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #007bff;
}

.orcamento-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.orcamento-item-title h6 {
  color: #344767;
  font-weight: 600;
  margin-bottom: 0.25rem;
  font-size: 0.85rem;
}

.orcamento-item-title small {
  color: #8898aa;
  font-size: 0.7rem;
}

.orcamento-item-body {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.orcamento-item-info {
  flex: 1;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
  font-size: 0.7rem;
  color: #8898aa;
}

.orcamento-item-valor {
  text-align: right;
}

.valor-principal {
  font-size: 0.9rem;
  font-weight: 700;
  color: #2dce89;
}

.valor-original {
  display: block;
  font-size: 0.7rem;
  text-decoration: line-through;
  margin-top: 0.25rem;
}

.orcamento-item-actions {
  display: flex;
  gap: 0.25rem;
  align-items: center;
  justify-content: flex-end;
}

.orcamento-item-actions .btn {
  padding: 0.2rem 0.4rem;
  font-size: 0.7rem;
}

/* ========== CARDS DE ESTATÍSTICAS MINI ========== */
.stats-card-mini {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  height: 55px;
}

.stats-card-mini:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.stats-card-mini .stats-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.5rem;
  font-size: 0.9rem;
  background: rgba(0, 0, 0, 0.05);
}

.stats-card-mini .stats-content {
  flex: 1;
}

.stats-card-mini .stats-title {
  font-size: 0.65rem;
  font-weight: 600;
  color: #8898aa;
  text-transform: uppercase;
  margin-bottom: 0.2rem;
  letter-spacing: 0.3px;
}

.stats-card-mini .stats-value {
  font-size: 0.8rem;
  font-weight: 700;
}

/* Manter compatibilidade com cards compactos existentes */
.stats-card-compact {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 0.75rem;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  height: 70px;
}

.stats-card-compact:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.stats-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  font-size: 1.1rem;
  background: rgba(0, 0, 0, 0.05);
}

.stats-content {
  flex: 1;
}

.stats-card-compact .stats-title {
  font-size: 0.7rem;
  font-weight: 600;
  color: #8898aa;
  text-transform: uppercase;
  margin-bottom: 0.25rem;
  letter-spacing: 0.5px;
}

.stats-card-compact .stats-value {
  font-size: 0.95rem;
  font-weight: 700;
}

/* ========== CARDS TOGGLE DE STATUS ========== */
.stats-card-toggle {
  background: #fff;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  padding: 0.6rem;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  height: 65px;
  cursor: pointer;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.stats-card-toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.stats-card-toggle.disabled {
  border-color: #e9ecef;
  background: #f8f9fa;
  opacity: 0.6;
}

.stats-card-toggle .stats-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.6rem;
  font-size: 1rem;
  background: rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.stats-card-toggle .stats-content {
  flex: 1;
}

.stats-card-toggle .stats-title {
  font-size: 0.68rem;
  font-weight: 600;
  color: #8898aa;
  text-transform: uppercase;
  margin-bottom: 0.25rem;
  letter-spacing: 0.3px;
  transition: all 0.3s ease;
}

.stats-card-toggle .stats-value {
  font-size: 0.85rem;
  font-weight: 700;
  transition: all 0.3s ease;
}

.stats-card-toggle .toggle-checkbox {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 18px;
  height: 18px;
  border: 2px solid #dee2e6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  transition: all 0.3s ease;
  font-size: 10px;
}

.stats-card-toggle.disabled .stats-title,
.stats-card-toggle.disabled .stats-value {
  color: #8898aa !important;
}

.stats-card-toggle.disabled .stats-icon {
  background: rgba(0, 0, 0, 0.03);
}

/* Estilos específicos para cada status quando ativo */
.stats-card-pago.active {
  border-color: rgba(40, 167, 69, 0.4);
  background: rgba(40, 167, 69, 0.02);
}

.stats-card-pago.active .toggle-checkbox {
  border-color: #28a745;
  background: #28a745;
  color: white;
}

.stats-card-pendente.active {
  border-color: rgba(255, 193, 7, 0.4);
  background: rgba(255, 193, 7, 0.02);
}

.stats-card-pendente.active .toggle-checkbox {
  border-color: #ffc107;
  background: #ffc107;
  color: white;
}

.stats-card-vencido.active {
  border-color: rgba(220, 53, 69, 0.4);
  background: rgba(220, 53, 69, 0.02);
}

.stats-card-vencido.active .toggle-checkbox {
  border-color: #dc3545;
  background: #dc3545;
  color: white;
}

.stats-card-cancelado.active {
  border-color: rgba(108, 117, 125, 0.4);
  background: rgba(108, 117, 125, 0.02);
}

.stats-card-cancelado.active .toggle-checkbox {
  border-color: #6c757d;
  background: #6c757d;
  color: white;
}

/* ========== HEADER GRADIENTES ========== */
.bg-gradient-primary {
  background: linear-gradient(87deg, #5e72e4 0, #825ee4 100%);
}

.bg-gradient-blue {
  background: linear-gradient(87deg, #11cdef 0, #1171ef 100%);
}

.bg-gradient-blue-subtle {
  background: linear-gradient(135deg, rgba(17, 205, 239, 0.08) 0%, rgba(17, 113, 239, 0.12) 100%);
  border-bottom: 1px solid rgba(17, 205, 239, 0.15);
}

/* ========== CAMPO DE BUSCA COM ÍCONE ========== */
.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 14px;
  z-index: 2;
  transition: color 0.3s ease;
}

.search-input-with-icon {
  padding-left: 36px !important;
  transition: all 0.3s ease;
}

.search-input-with-icon:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.search-input-with-icon:focus + .search-input-icon,
.search-input-wrapper:focus-within .search-input-icon {
  color: #007bff;
}

/* ========== RESPONSIVIDADE ========== */
@media (max-width: 991px) {
  .row.g-0.h-100 {
    min-height: auto;
  }

  .col-lg-4.col-md-5,
  .col-lg-8.col-md-7 {
    border-bottom: 1px solid #e9ecef;
  }

  .col-lg-4.col-md-5 {
    border-right: none;
  }

  .orcamentos-section {
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .row.g-0.h-100 {
    flex-direction: column;
  }

  .col-lg-4.col-md-5,
  .col-lg-8.col-md-7 {
    max-width: 100%;
    flex: none;
  }

  .orcamentos-section {
    max-height: 300px;
  }

  .section-header {
    padding: 0.4rem 0.75rem;
  }

  .filters-section,
  .stats-section {
    padding: 0.5rem 0.75rem;
  }

  .stats-card-toggle {
    height: 58px;
    padding: 0.45rem;
  }

  .stats-card-toggle .stats-icon {
    width: 28px;
    height: 28px;
    font-size: 0.85rem;
    margin-right: 0.45rem;
  }

  .stats-card-toggle .stats-title {
    font-size: 0.62rem;
  }

  .stats-card-toggle .stats-value {
    font-size: 0.78rem;
  }

  .stats-card-toggle .toggle-checkbox {
    width: 16px;
    height: 16px;
    top: 6px;
    right: 6px;
    font-size: 9px;
  }

  .search-input-icon {
    font-size: 13px;
    left: 10px;
  }

  .search-input-with-icon {
    padding-left: 32px !important;
  }

  .orcamento-item {
    padding: 0.5rem;
  }

  .orcamento-item-title h6 {
    font-size: 0.8rem;
  }

  .stats-card-mini {
    height: 50px;
    padding: 0.4rem;
  }

  .stats-card-mini .stats-icon {
    width: 25px;
    height: 25px;
    font-size: 0.8rem;
    margin-right: 0.4rem;
  }

  .stats-card-mini .stats-title {
    font-size: 0.6rem;
  }

  .stats-card-mini .stats-value {
    font-size: 0.75rem;
  }
}
</style>
